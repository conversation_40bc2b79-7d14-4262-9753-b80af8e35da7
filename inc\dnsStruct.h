#pragma once

#include "../inc/header.h"
#include "../inc/LRU.h"

/*
    本头文件专门用于存放DNS报文结构体Message的定义，以及一切有关DNS报文的操作
    DNS 报文格式如下：
    +---------------------+
    |        Header       | 报文头，固定12字节，由结构体DNS_header存储
    +---------------------+
    |       Question      | 向域名服务器的查询请求，由结构体DNS_question存储
    +---------------------+
    |        Answer       | 对于查询问题的回复
    +---------------------+
    |      Authority      | 指向授权域名服务器
    +---------------------+
    |      Additional     | 附加信息
    +---------------------+
    后面三个部分由结构体DNS_resource_record存储
*/
typedef struct DNS_message
{
    struct DNS_header *header;
    struct DNS_question *question;
    struct DNS_resource_record *answer;
    struct DNS_resource_record *authority;
    struct DNS_resource_record *additional;
} DNS_message;
/*
    报文头部结构体
    +-----------------------------------+
    |     事务id         |  标志位flags  |
    +-----------------------------------+
    |  问题数ques_num    | 回答数ans_num |
    +-----------------------------------+
    |  授权数auth_num    | 附加数add_num |
    +-----------------------------------+
    //DNS 报文的 ID 标识。对于请求报文和其对应的应答报文，该字段的值是相同的。通过它可以区分 DNS
   应答报文是对哪个请求进行响应的。 标志位结构体
    +---+---+---+---+---+---+---+---+----+
    |QR |OPCODE|AA |TC |RD |RA |Z  |RCODE|
    +---+---+---+---+---+---+---+---+----+
    QR：1位，查询/响应标志位。0表示查询报文，1表示响应报文。
    OPCODE：4位，操作码。通常取值为0，表示标准查询；取值为1，表示反向查询；取值为2，表示服务器状态查询。
    AA：1位，授权回答。该位在响应报文中有效，表示响应的服务器是一个权威服务器。
    TC：1位，截断。指示报文是否被截断。
    RD：1位，期望递归。表示发出查询的主机希望 DNS 服务器递归查询。
    RA：1位，可用递归。表示 DNS 服务器支持递归查询。
    Z：3位，保留字段。
    RCODE：4位，返回码。表示响应的返回状态。通常取值为0，表示没有错误；取值为3，表示名字错误。
 */
typedef struct DNS_header
{
    uint16_t transactionID; // 事务ID
    uint16_t flags;         // 标志位
    uint16_t ques_num;      // 问题数
    uint16_t ans_num;       // 回答数
    uint16_t auth_num;      // 授权数
    uint16_t add_num;       // 附加数
} DNS_header;

/*
    DNS问题结构体
    +---------------------+
    |        QNAME        | 查询的域名
    +---------------------+
    |        QTYPE        | 查询类型
    +---------------------+
    |        QCLASS       | 查询类
    +---------------------+
 */
typedef struct DNS_question
{
    char *qname;     // 查询的域名
    uint16_t qtype;  // 查询类型（如 A、AAAA、CNAME 等）
    uint16_t qclass; // 查询类（通常为 IN，表示互联网）
} DNS_question;

/*
    资源记录的 RDATA 部分可以是多种类型的，具体取决于记录的类型（如 A、AAAA、CNAME 等）。
    这里使用联合体来表示不同类型的 RDATA 数据。
    +--------------------+
    |        NAME        | 资源数据
    +--------------------+
    |        TYPE        | 资源数据类型
    +--------------------+
    |        CLASS       | 资源数据类
    +--------------------+
    |        TTL         | 生存时间（TTL）
    +--------------------+
    |      RDLENGTH      | 资源数据长度
    +--------------------+
    |        RDATA       | 资源数据
    +--------------------+

*/

union ResourceData
{
    /* IPv4 */
    struct
    {
        uint8_t IP_addr[4];
    } a_record;

    //-----------------------------------------
    /* IPv6 */
    struct
    {
        uint8_t IP_addr[16];
    } aaaa_record;
    //-----------------------------------------

    /* SOA：权威记录的起始 */
    struct
    {
        char *MName;      // 主服务器域名
        char *RName;      // 管理员邮箱
        uint32_t serial;  // 版本号
        uint32_t refresh; // 刷新数据间隔
        uint32_t retry;   // 重试间隔
        uint32_t expire;  // 超时重传时间
        uint32_t minimum; // 默认生存时间
    } soa_record;

    /* cname规范名称记录 */
    struct
    {
        char *name;
    } cname_record;
};

typedef struct DNS_resource_record
{
    char *name;              // 资源记录的名称
    uint16_t type;           // 资源记录的类型（如 A、AAAA、CNAME 等）
    uint16_t class;          // 资源记录的类（通常为 IN，表示互联网）
    uint32_t ttl;            // 生存时间（TTL）
    uint16_t rdlength;       // RDATA 的长度
    union ResourceData data; // 资源数据，根据 type 的不同而不同
} DNS_resource_record;

// 以下是DNS报文头部的各个字段的掩码
static const uint16_t QR_MASK = 0x8000;
static const uint16_t OPCODE_MASK = 0x7800;
static const uint16_t AA_MASK = 0x0400;
static const uint16_t TC_MASK = 0x0200;
static const uint16_t RD_MASK = 0x0100;
static const uint16_t RA_MASK = 0x0080;
static const uint16_t RCODE_MASK = 0x000F;

void parse_dns_packet(DNS_message *msg, const char *buffer, int length);
char *parse_dns_name(const char *buffer, int *offset, int max_length);
void parse_resource_record(const char *buffer, int *offset, int max_length, DNS_resource_record *rr);

int build_dns_query(char *buf, const char *host);
// int dns_message_to_wire(DNS_message *msg, unsigned char *buffer, int buf_size);
// int build_cached_response(unsigned char *buffer,
//                           int buf_size,
//                           uint16_t transactionID,
//                           const char *query_name,
//                           uint16_t query_type,
//                           DNSRecord *cached_record);
int build_dns_response(unsigned char *request, int requestLen, const char *ip);

void get_dns_message(DNS_message *message, const unsigned char *buffer, size_t length);
