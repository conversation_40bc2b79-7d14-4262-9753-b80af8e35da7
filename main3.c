#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <time.h>
#include <winsock2.h> // Windows 网络编程头文件
#include <ws2tcpip.h> // 用于 inet_pton/inet_ntop

#pragma comment(lib, "ws2_32.lib") // 链接 Winsock 库

#define DOMAIN_MAX_LEN 256
#define IP_STR_MAX_LEN 46 // IPv6最长字符串格式

// IP地址联合体(支持IPv4和IPv6)
typedef union
{
    uint32_t ipv4;
    uint8_t ipv6[16];
} IPAddress;

// DNS记录结构
typedef struct DNSRecord
{
    char domain[DOMAIN_MAX_LEN];
    int addr_type; // AF_INET或AF_INET6
    IPAddress address;
    time_t expire_time;                    // 过期时间戳
    time_t last_used;                      // 最后使用时间(LRU用)
    struct DNSRecord *next;                // 哈希链表指针
    struct DNSRecord *lru_prev, *lru_next; // LRU链表指针
} DNSRecord;

// Trie树节点
typedef struct TrieNode
{
    struct TrieNode *children[26]; // a-z
    DNSRecord *record;             // 指向实际记录
    int is_end;                    // 是否是域名结尾
} TrieNode;

// DNS缓存结构
typedef struct DNSCache
{
    TrieNode *root;      // Trie树根节点
    DNSRecord *lru_head; // LRU链表头(最近使用)
    DNSRecord *lru_tail; // LRU链表尾(最久未使用)
    int capacity;        // 缓存容量
    int size;            // 当前大小
} DNSCache;

// 创建新的Trie节点
TrieNode *create_trie_node()
{
    TrieNode *node = (TrieNode *)malloc(sizeof(TrieNode));
    memset(node->children, 0, sizeof(node->children));
    node->record = NULL;
    node->is_end = 0;
    return node;
}

// 初始化DNS缓存
DNSCache *init_dns_cache(int capacity)
{
    DNSCache *cache = (DNSCache *)malloc(sizeof(DNSCache));
    cache->root = create_trie_node();
    cache->lru_head = cache->lru_tail = NULL;
    cache->capacity = capacity;
    cache->size = 0;
    return cache;
}

// 域名转换为小写并验证
void normalize_domain(char *domain)
{
    for (int i = 0; domain[i]; i++)
    {
        if (domain[i] >= 'A' && domain[i] <= 'Z')
        {
            domain[i] += 'a' - 'A';
        }
        else if (domain[i] != '.' && domain[i] != '-' &&
                 !(domain[i] >= 'a' && domain[i] <= 'z') &&
                 !(domain[i] >= '0' && domain[i] <= '9'))
        {
            domain[i] = '\0'; // 非法字符截断
            break;
        }
    }
}

// 向Trie树插入记录
void trie_insert(DNSCache *cache, DNSRecord *record)
{
    char domain[DOMAIN_MAX_LEN];
    strcpy(domain, record->domain);
    normalize_domain(domain);

    TrieNode *curr = cache->root;
    for (int i = strlen(domain) - 1; i >= 0; i--)
    { // 反向插入(便于后缀匹配)
        char c = domain[i];
        if (c == '.')
            continue;

        int index = c - 'a';
        if (index < 0 || index >= 26)
            continue;

        if (!curr->children[index])
        {
            curr->children[index] = create_trie_node();
        }
        curr = curr->children[index];
    }

    curr->is_end = 1;
    curr->record = record;
}

// 从Trie树查找记录
DNSRecord *trie_search(DNSCache *cache, const char *domain)
{
    char normalized[DOMAIN_MAX_LEN];
    strcpy(normalized, domain);
    normalize_domain(normalized);

    TrieNode *curr = cache->root;
    for (int i = strlen(normalized) - 1; i >= 0; i--)
    {
        char c = normalized[i];
        printf("%c\n", c);
        if (c == '.')
            continue;

        int index = c - 'a';
        if (index < 0 || index >= 26)
            return NULL;

        if (!curr->children[index])
        {
            return NULL;
        }
        curr = curr->children[index];
    }

    return (curr && curr->is_end) ? curr->record : NULL;
}

// 更新LRU链表(将记录移到头部)
void lru_update(DNSCache *cache, DNSRecord *record)
{
    if (record == cache->lru_head)
        return;

    // 从原位置移除
    if (record->lru_prev)
        record->lru_prev->lru_next = record->lru_next;
    if (record->lru_next)
        record->lru_next->lru_prev = record->lru_prev;

    // 如果是尾节点且不是唯一节点
    if (record == cache->lru_tail && record->lru_prev)
    {
        cache->lru_tail = record->lru_prev;
    }

    // 插入到头部
    record->lru_next = cache->lru_head;
    record->lru_prev = NULL;

    if (cache->lru_head)
    {
        cache->lru_head->lru_prev = record;
    }
    cache->lru_head = record;

    // 如果之前缓存为空
    if (!cache->lru_tail)
    {
        cache->lru_tail = record;
    }

    record->last_used = time(NULL);
}

// 淘汰最久未使用的记录
void lru_evict(DNSCache *cache)
{
    if (!cache->lru_tail || cache->size < cache->capacity)
        return;

    DNSRecord *to_evict = cache->lru_tail;

    // 从Trie树中删除
    char normalized[DOMAIN_MAX_LEN];
    strcpy(normalized, to_evict->domain);
    normalize_domain(normalized);

    TrieNode *curr = cache->root;
    for (int i = strlen(normalized) - 1; i >= 0; i--)
    {
        char c = normalized[i];
        if (c == '.')
            continue;

        int index = c - 'a';
        if (index < 0 || index >= 26)
            break;

        if (!curr->children[index])
            break;
        curr = curr->children[index];
    }

    if (curr && curr->is_end)
    {
        curr->is_end = 0;
        curr->record = NULL;
    }

    // 从LRU链表中移除
    if (to_evict->lru_prev)
    {
        to_evict->lru_prev->lru_next = NULL;
    }
    cache->lru_tail = to_evict->lru_prev;

    if (to_evict == cache->lru_head)
    {
        cache->lru_head = NULL;
    }

    free(to_evict);
    cache->size--;
}

// 添加记录到缓存
void add_to_cache(DNSCache *cache, const char *domain, int addr_type, void *addr, time_t ttl)
{
    // 先检查是否已存在
    DNSRecord *existing = trie_search(cache, domain);
    if (existing)
    {
        // 更新现有记录
        existing->addr_type = addr_type;
        if (addr_type == AF_INET)
        {
            existing->address.ipv4 = *(uint32_t *)addr;
        }
        else
        {
            memcpy(existing->address.ipv6, addr, 16);
        }
        existing->expire_time = time(NULL) + ttl;
        lru_update(cache, existing);
        return;
    }

    // 如果缓存已满，淘汰最久未使用的记录
    if (cache->size >= cache->capacity)
    {
        lru_evict(cache);
    }

    // 创建新记录
    DNSRecord *record = (DNSRecord *)malloc(sizeof(DNSRecord));
    strncpy(record->domain, domain, DOMAIN_MAX_LEN - 1);
    record->domain[DOMAIN_MAX_LEN - 1] = '\0';
    record->addr_type = addr_type;

    if (addr_type == AF_INET)
    {
        record->address.ipv4 = *(uint32_t *)addr;
    }
    else
    {
        memcpy(record->address.ipv6, addr, 16);
    }

    record->expire_time = time(NULL) + ttl;
    record->last_used = time(NULL);
    record->next = NULL;
    record->lru_prev = record->lru_next = NULL;

    // 添加到Trie树
    trie_insert(cache, record);

    // 添加到LRU链表头部
    lru_update(cache, record);

    cache->size++;
}

// 从缓存查询记录
DNSRecord *query_cache(DNSCache *cache, const char *domain)
{
    DNSRecord *record = trie_search(cache, domain);
    if (!record)
        return NULL;

    // 检查是否过期
    if (time(NULL) > record->expire_time)
    {
        // 从缓存中移除过期记录
        lru_evict(cache);
        return NULL;
    }

    // 更新LRU
    lru_update(cache, record);

    return record;
}

// 打印缓存状态(调试用)
void print_cache_status(DNSCache *cache)
{
    printf("Cache status: %d/%d\n", cache->size, cache->capacity);
    printf("LRU order: ");

    DNSRecord *curr = cache->lru_head;
    while (curr)
    {
        printf("%s -> ", curr->domain);
        curr = curr->lru_next;
    }
    printf("NULL\n");
}

// 示例使用
int main()
{
    // 初始化Winsock
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0)
    {
        fprintf(stderr, "WSAStartup failed\n");
        return 1;
    }

    DNSCache *cache = init_dns_cache(5); // 容量为5

    // 添加一些记录
    uint32_t ipv4;
    inet_pton(AF_INET, "***********", &ipv4);
    add_to_cache(cache, "example.com", AF_INET, &ipv4, 3600);

    uint8_t ipv6[16];
    // 将文本形式的 IP 地址转换为二进制形式的函数
    inet_pton(AF_INET6, "2001:db8:85a3::8a2e:370:7334", ipv6);
    add_to_cache(cache, "ipv6.example.com", AF_INET6, ipv6, 7200);

    // 查询记录
    DNSRecord *found = query_cache(cache, "example.com");
    if (found)
    {
        char ip_str[IP_STR_MAX_LEN];
        if (found->addr_type == AF_INET)
        {
            inet_ntop(AF_INET, &found->address.ipv4, ip_str, IP_STR_MAX_LEN);
        }
        else
        {
            inet_ntop(AF_INET6, found->address.ipv6, ip_str, IP_STR_MAX_LEN);
        }
        printf("Found: %s -> %s\n", found->domain, ip_str);
    }

    print_cache_status(cache);

    WSACleanup(); // 清理Winsock
    return 0;
}