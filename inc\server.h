#pragma once

#include "../inc/cache.h"
#include "../inc/dnsStruct.h"
#include "../inc/header.h"
#include "../inc/blacklist.h"

#define PORT 53
#define BUF_SIZE 512

int client_sock; // 客户端socket
int server_sock; // 服务端socket

struct sockaddr_in client_addr; // 客户端地址
struct sockaddr_in server_addr; // 服务端地址

int addr_len;     // 地址长度
char *remote_dns; // 远程主机ip地址

WSADATA wsaData;
SOCKET sock;
struct sockaddr_in serverAddr, clientAddr;
int addrLen;
char buffer[BUF_SIZE];

void init_socket(int port);
void poll(void);
void receiveClient(void);
void free_temp_record_chain(DNSRecord* first_record);
void receiveServer(void);
int build_nxdomain_response(unsigned char* buffer, int buf_size, uint16_t transactionID, const char* query_name, uint16_t query_type);