# DNS服务器调试标准

## 环境准备

### 1. 编译与启动
```bash
# 编译项目
make

# 启动DNS服务器
./dnsserver.exe

# 检查服务器进程状态
tasklist | findstr dnsserver
```

### 2. 基础连通性测试
```bash
# 测试本地DNS服务器连接
nslookup www.baidu.com 127.0.0.1

# 测试远程DNS转发功能
nslookup example.com 127.0.0.1
```

## 功能测试清单

### 1. IPv4地址解析测试
```bash
# 测试A记录查询
nslookup www.baidu.com 127.0.0.1

# 预期结果：查看有没有返回多个ipv4的地址
```

### 2. IPv6地址解析测试
```bash
# 测试AAAA记录查询
nslookup -type=AAAA www.bupt.edu.cn 127.0.0.1

# 预期结果：有没有返回ipv4地址的同时返回ipv6的地址
```

### 3. CNAME记录解析测试
```bash
# 测试CNAME记录查询
nslookup -type=CNAME www.baidu.com 127.0.0.1

# 预期结果：
# 在客户端的终端显示：www.baidu.com   canonical name = www.a.shifen.com

# 测试CNAME链解析（A记录查询应同时返回CNAME和A记录）
nslookup www.baidu.com 127.0.0.1

# 正常会将type:1 type:5全部存储在链表当中

# 预期结果：
# - 在服务器的终端显示CNAME记录：domain:www.a.shifen.com type:1-> domain:www.a.shifen.com type:1-> domain:www.baidu.com type:5
# - 显示最终A记录：vn46.bupt.edu.cn -> IP地址
```

### 4. 缓存功能测试
```bash
# 第一次查询（缓存未命中）
nslookup www.bilibili.com 127.0.0.1

# 第二次查询（缓存命中）
nslookup www.bilibiili.com 127.0.0.1

# 预期结果：
# - 第一次查询：日志显示"Cache miss"和"forwarding to remote DNS"
# - 正常会将每个得到的type值全都存储在Cache当中
# - 服务器终端显示：domain:bilibili.com type:1-> domain:bilibili.com type:1-> domain:bilibili.com type:1-> domain:bilibili.com type:1
# - 第二次查询：日志显示"Cache hit"，响应更快
```

### 5. 黑名单功能测试
```bash
# 测试被拦截的域名
nslookup facebook.com 127.0.0.1

# 预期结果：*** UnKnown 找不到 facebook.com: Non-existent domain"
```

### 6. 多记录响应测试
```bash
# 测试返回多个IP地址的域名
nslookup www.baidu.com 127.0.0.1

# 预期结果：客户端应该返回包括别名以及ipv4和ipv6在内的所用信息
# 客户端终端运行结果：
服务器:  UnKnown
Address:  127.0.0.1

非权威应答:
名称:    www.a.shifen.com
Addresses:  *************
          ***************
Aliases:  www.baidu.com
```

### 7. hosts文件加载测试
```bash
# 查询hosts文件中的域名
nslookup server.local 127.0.0.1
nslookup ipv6.google.com 127.0.0.1

# 预期结果：返回hosts文件中配置的IP地址
```

## 错误排查

### 1. 常见问题诊断
- **服务器无响应**：检查端口53是否被占用，确认防火墙设置
- **IPv6不返回**：检查Additional Section处理，确认远程DNS是否返回IPv6记录或者ipv6记录是否正确被存储在Cache当中
- **CNAME链断裂**：检查缓存中是否有完整的CNAME链记录
- **内存泄漏**：确认临时链表是否正确释放

### 2. 日志分析要点
- **缓存状态**：观察"Cache hit/miss"日志
- **记录类型**：确认查询和响应的记录类型匹配
- **TTL值**：检查缓存记录的过期时间
- **链表操作**：观察CNAME链的构建过程

### 3. 性能监控
```bash
# 监控DNS查询延迟
time nslookup www.bilibili.com 127.0.0.1

# 检查缓存利用率（观察日志中的缓存状态信息）
```

## 回归测试

### 核心功能验证
1. ✅ 基本DNS查询功能
2. ✅ CNAME记录正确解析
3. ✅ IPv4/IPv6双栈支持
4. ✅ 缓存机制有效性
5. ✅ 黑名单拦截功能
6. ✅ 多记录响应处理
7. ✅ 远程DNS转发
8. ✅ hosts文件加载

### 边界条件测试
- 长域名查询
- 深层CNAME链（防止循环）
- 大量并发查询
- 缓存溢出处理
- 无效查询处理

## 调试技巧

### 1. 详细日志输出
服务器启动后观察控制台输出，关键日志包括：
- DNS包接收信息
- 缓存命中/未命中状态
- 记录类型和内容
- CNAME链解析过程

### 2. 网络抓包分析
```bash
# 使用Wireshark抓取DNS包
# 例如nslookup百度网页时，可以通过以下命令查看远程服务器返回给客户端的信息是什么，通过对比自己-ddd调试等级之下的打印出来的字节流看看有什么区别
dns.qry.name == "www.baidu.com" && ip.dst == ************ && ip.src == ********

```

### 3. 内存和性能分析
- 检查高并发情况之下能不能正常运行，即所使用的ID映射表有没有起到作用