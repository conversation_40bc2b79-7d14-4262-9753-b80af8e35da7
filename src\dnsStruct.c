#include "../inc/dnsStruct.h"
#include "../inc/default.h"

// 解析DNS 报文
void parse_dns_packet(DNS_message *msg, const char *buffer, int length)
{
    if (length < 12)
    {
        printf("Packet too short for DNS header.\n");
        return;
    }

    // Parse DNS header
    unsigned short transactionID = (buffer[0] << 8) | buffer[1];
    unsigned short flags = (buffer[2] << 8) | buffer[3];
    unsigned short qdCount = (buffer[4] << 8) | buffer[5];
    unsigned short anCount = (buffer[6] << 8) | buffer[7];
    unsigned short nsCount = (buffer[8] << 8) | buffer[9];
    unsigned short arCount = (buffer[10] << 8) | buffer[11];

    msg->header = malloc(sizeof(DNS_header));
    if (!msg->header)
    {
        printf("Memory allocation failed for DNS header.\n");
        return;
    }
    msg->header->transactionID = transactionID;
    msg->header->flags = flags;
    msg->header->ques_num = qdCount;
    msg->header->ans_num = anCount;
    msg->header->auth_num = nsCount;
    msg->header->add_num = arCount;

    // printf("Transaction ID: 0x%04X\n", transactionID);
    // printf("Flags: 0x%04X\n", flags);
    // printf("Questions: %d, Answers: %d, Authority: %d, Additional: %d\n",
    //        qdCount, anCount, nsCount, arCount);

    // // Parse flags
    // printf("  QR: %s\n", (flags & QR_MASK) ? "Response" : "Query");
    // printf("  OPCODE: %u\n", (flags & OPCODE_MASK) >> 11);
    // printf("  AA: %d\n", (flags & AA_MASK) ? 1 : 0);
    // printf("  TC: %d\n", (flags & TC_MASK) ? 1 : 0);
    // printf("  RD: %d\n", (flags & RD_MASK) ? 1 : 0);
    // printf("  RA: %d\n", (flags & RA_MASK) ? 1 : 0);
    // printf("  RCODE: %u\n", flags & RCODE_MASK);

    int offset = 12;

    // Parse questions section
    if (qdCount > 0)
    {
        msg->question = malloc(qdCount * sizeof(DNS_question));
        if (!msg->question)
        {
            printf("Memory allocation failed for questions.\n");
            return;
        }

        for (int i = 0; i < qdCount; i++)
        {
            printf("\nQuestion #%d:\n", i + 1);
            msg->question[i].qname = parse_dns_name(buffer, &offset, length);
            if (!msg->question[i].qname)
            {
                printf("Failed to parse question name.\n");
                return;
            }
            printf("  Name: %s\n", msg->question[i].qname);

            if (offset + 4 > length)
            {
                printf("Incomplete question section.\n");
                return;
            }

            msg->question[i].qtype = (buffer[offset] << 8) | buffer[offset + 1];
            msg->question[i].qclass = (buffer[offset + 2] << 8) | buffer[offset + 3];
            offset += 4;

            printf("  Type: %u\n", msg->question[i].qtype);
            printf("  Class: %u\n", msg->question[i].qclass);
        }
    }

    // Parse answers section
    if (anCount > 0)
    {
        msg->answer = malloc(anCount * sizeof(DNS_resource_record));
        if (!msg->answer)
        {
            printf("Memory allocation failed for answers.\n");
            return;
        }

        printf("\nAnswers:\n");
        for (int i = 0; i < anCount; i++)
        {
            printf("\nAnswer #%d:\n", i + 1);
            parse_resource_record(buffer, &offset, length, &msg->answer[i]);
        }
    }

    // Parse authority section
    if (nsCount > 0)
    {
        msg->authority = malloc(nsCount * sizeof(DNS_resource_record));
        if (!msg->authority)
        {
            printf("Memory allocation failed for authority records.\n");
            return;
        }

        printf("\nAuthority Records:\n");
        for (int i = 0; i < nsCount; i++)
        {
            printf("\nAuthority Record #%d:\n", i + 1);
            parse_resource_record(buffer, &offset, length, &msg->authority[i]);
        }
    }

    // Parse additional section
    if (arCount > 0)
    {
        msg->additional = malloc(arCount * sizeof(DNS_resource_record));
        if (!msg->additional)
        {
            printf("Memory allocation failed for additional records.\n");
            return;
        }

        printf("\nAdditional Records:\n");
        for (int i = 0; i < arCount; i++)
        {
            printf("\nAdditional Record #%d:\n", i + 1);
            parse_resource_record(buffer, &offset, length, &msg->additional[i]);
        }
    }
}

// Helper function to parse DNS names (including compressed names)
char *parse_dns_name(const char *buffer, int *offset, int max_length)
{
    char name[256]; // Maximum DNS name length
    int name_pos = 0;
    int initial_offset = *offset;
    int jumps = 0;

    while (1)
    {
        if (*offset >= max_length)
        {
            return NULL; // Invalid packet
        }

        unsigned int len = (unsigned char)buffer[(*offset)++];

        if (len == 0)
        {
            // End of name
            if (name_pos == 0)
            {
                name[name_pos++] = '.';
            }
            name[name_pos] = '\0';
            break;
        }

        // Check for DNS name compression (two high bits set)
        if ((len & 0xC0) == 0xC0)
        {
            if (*offset >= max_length)
            {
                return NULL; // Invalid packet
            }

            // Get the offset pointer
            int ptr_offset = ((len & 0x3F) << 8) | (unsigned char)buffer[(*offset)++];
            if (ptr_offset >= initial_offset)
            {
                return NULL; // Invalid compression pointer
            }

            // Follow the pointer (but limit jumps to prevent infinite loops)
            if (jumps++ > 10)
            {
                return NULL; // Too many jumps
            }

            int saved_offset = *offset;
            *offset = ptr_offset;
            char *rest = parse_dns_name(buffer, offset, max_length);
            *offset = saved_offset;

            if (!rest)
            {
                return NULL;
            }

            if (name_pos > 0)
            {
                name[name_pos++] = '.';
            }
            strcpy(name + name_pos, rest);
            name_pos += strlen(rest);
            free(rest);
            break;
        }

        // Normal label
        if (name_pos > 0)
        {
            name[name_pos++] = '.';
        }

        if (*offset + len > max_length)
        {
            return NULL; // Invalid packet
        }

        memcpy(name + name_pos, buffer + *offset, len);
        *offset += len;
        name_pos += len;
    }

    return strdup(name);
}

// Helper function to parse resource records
void parse_resource_record(const char *buffer, int *offset, int max_length, DNS_resource_record *rr)
{
    rr->name = parse_dns_name(buffer, offset, max_length);
    if (!rr->name)
    {
        printf("Failed to parse resource record name.\n");
        return;
    }
    printf("  Name: %s\n", rr->name);

    if (*offset + 10 > max_length)
    {
        printf("Incomplete resource record.\n");
        return;
    }

    rr->type = (buffer[*offset] << 8) | buffer[*offset + 1];
    rr->class = (buffer[*offset + 2] << 8) | buffer[*offset + 3];
    rr->ttl =
        (buffer[*offset + 4] << 24) | (buffer[*offset + 5] << 16) | (buffer[*offset + 6] << 8) | buffer[*offset + 7];
    rr->rdlength = (buffer[*offset + 8] << 8) | buffer[*offset + 9];
    *offset += 10;

    printf("  Type: %u\n", rr->type);
    printf("  Class: %u\n", rr->class);
    printf("  TTL: %u\n", rr->ttl);
    printf("  RD Length: %u\n", rr->rdlength);

    if (*offset + rr->rdlength > max_length)
    {
        printf("Invalid RDATA length.\n");
        return;
    }

    // Parse RDATA based on type
    switch (rr->type)
    {
    case 1: // A record (IPv4)
        if (rr->rdlength == 4)
        {
            for (int i = 0; i < 4; i++)
            {
                rr->data.a_record.IP_addr[i] = buffer[(*offset)++];
            }
            printf("  Address: %u.%u.%u.%u\n", rr->data.a_record.IP_addr[0], rr->data.a_record.IP_addr[1],
                   rr->data.a_record.IP_addr[2], rr->data.a_record.IP_addr[3]);
        }
        break;

    case 28: // AAAA record (IPv6)
        if (rr->rdlength == 16)
        {
            for (int i = 0; i < 16; i++)
            {
                rr->data.aaaa_record.IP_addr[i] = buffer[(*offset)++];
            }
            printf("  Address: ");
            for (int i = 0; i < 16; i++)
            {
                printf("%02x", rr->data.aaaa_record.IP_addr[i]);
                if (i % 2 == 1 && i != 15)
                    printf(":");
            }
            printf("\n");
        }
        break;

    case 5: // CNAME record
        rr->data.cname_record.name = parse_dns_name(buffer, offset, max_length);
        if (rr->data.cname_record.name)
        {
            printf("  CNAME: %s\n", rr->data.cname_record.name);
        }
        break;

    case 6: // SOA record
        rr->data.soa_record.MName = parse_dns_name(buffer, offset, max_length);
        rr->data.soa_record.RName = parse_dns_name(buffer, offset, max_length);
        if (*offset + 20 > max_length)
        {
            printf("Incomplete SOA record.\n");
            return;
        }
        rr->data.soa_record.serial = (buffer[*offset] << 24) | (buffer[*offset + 1] << 16) |
                                     (buffer[*offset + 2] << 8) | buffer[*offset + 3];
        rr->data.soa_record.refresh = (buffer[*offset + 4] << 24) | (buffer[*offset + 5] << 16) |
                                      (buffer[*offset + 6] << 8) | buffer[*offset + 7];
        rr->data.soa_record.retry = (buffer[*offset + 8] << 24) | (buffer[*offset + 9] << 16) |
                                    (buffer[*offset + 10] << 8) | buffer[*offset + 11];
        rr->data.soa_record.expire = (buffer[*offset + 12] << 24) | (buffer[*offset + 13] << 16) |
                                     (buffer[*offset + 14] << 8) | buffer[*offset + 15];
        rr->data.soa_record.minimum = (buffer[*offset + 16] << 24) | (buffer[*offset + 17] << 16) |
                                      (buffer[*offset + 18] << 8) | buffer[*offset + 19];
        *offset += 20;

        printf("  SOA MName: %s\n", rr->data.soa_record.MName);
        printf("  SOA RName: %s\n", rr->data.soa_record.RName);
        printf("  SOA Serial: %u\n", rr->data.soa_record.serial);
        printf("  SOA Refresh: %u\n", rr->data.soa_record.refresh);
        printf("  SOA Retry: %u\n", rr->data.soa_record.retry);
        printf("  SOA Expire: %u\n", rr->data.soa_record.expire);
        printf("  SOA Minimum: %u\n", rr->data.soa_record.minimum);
        break;

    default:
        // For unsupported types, just skip the data
        printf("  Unsupported record type, skipping data.\n");
        *offset += rr->rdlength;
        break;
    }
}

// int dns_message_to_wire(DNS_message *msg, unsigned char *buffer, int buf_size)
// {
//     if (!msg || !buffer || buf_size <= 0)
//         return -1;

//     int offset = 0;

//     // Write header
//     if (offset + 12 > buf_size)
//         return -1;
//     // memcpy(buffer + offset, &msg->header->transactionID, 2);
//     buffer[0] = (msg->header->transactionID >> 8) & 0xFF; // 高字节
//     buffer[1] = msg->header->transactionID & 0xFF;        // 低字节
//     offset += 2;
//     memcpy(buffer + offset, &msg->header->flags, 2);
//     offset += 2;
//     memcpy(buffer + offset, &msg->header->ques_num, 2);
//     offset += 2;
//     memcpy(buffer + offset, &msg->header->ans_num, 2);
//     offset += 2;
//     memcpy(buffer + offset, &msg->header->auth_num, 2);
//     offset += 2;
//     memcpy(buffer + offset, &msg->header->add_num, 2);
//     offset += 2;

//     // Write question (simplified - should handle name compression)
//     if (msg->question)
//     {
//         // Write domain name (simple non-compressed format)
//         const char *name = msg->question->qname;
//         while (*name)
//         {
//             char *dot = strchr(name, '.');
//             int len = dot ? (dot - name) : strlen(name);

//             if (offset + len + 1 > buf_size)
//                 return -1;
//             buffer[offset++] = len;
//             memcpy(buffer + offset, name, len);
//             offset += len;

//             name = dot ? dot + 1 : name + len;
//         }
//         buffer[offset++] = 0; // Null terminator

//         // Write QTYPE and QCLASS
//         if (offset + 4 > buf_size)
//             return -1;
//         memcpy(buffer + offset, &msg->question->qtype, 2);
//         offset += 2;
//         memcpy(buffer + offset, &msg->question->qclass, 2);
//         offset += 2;
//     }

//     // Write answer (simplified - should handle name compression)
//     if (msg->answer)
//     {
//         // Write domain name (use pointer compression)
//         if (offset + 2 > buf_size)
//             return -1;
//         buffer[offset++] = 0xC0; // Pointer flag
//         buffer[offset++] = 0x0C; // Pointer to question name

//         // Write TYPE, CLASS, TTL
//         if (offset + 10 > buf_size)
//             return -1;
//         memcpy(buffer + offset, &msg->answer->type, 2);
//         offset += 2;
//         memcpy(buffer + offset, &msg->answer->class, 2);
//         offset += 2;
//         memcpy(buffer + offset, &msg->answer->ttl, 4);
//         offset += 4;

//         // Write RDLENGTH and RDATA
//         if (msg->answer->type == htons(RR_A))
//         {
//             if (offset + 6 > buf_size)
//                 return -1;
//             uint16_t rdlength = htons(4);
//             memcpy(buffer + offset, &rdlength, 2);
//             offset += 2;
//             memcpy(buffer + offset, msg->answer->data.a_record.IP_addr, 4);
//             offset += 4;
//         }
//         else if (msg->answer->type == htons(RR_AAAA))
//         {
//             if (offset + 18 > buf_size)
//                 return -1;
//             uint16_t rdlength = htons(16);
//             memcpy(buffer + offset, &rdlength, 2);
//             offset += 2;
//             memcpy(buffer + offset, msg->answer->data.aaaa_record.IP_addr, 16);
//             offset += 16;
//         }
//         else if (msg->answer->type == htons(RR_CNAME))
//         {
//             // Similar to question name format
//             // (Implementation omitted for brevity)
//         }
//     }

//     return offset;
// }

// int build_cached_response(unsigned char *buffer,
//                           int buf_size,
//                           uint16_t transactionID,
//                           const char *query_name,
//                           uint16_t query_type,
//                           DNSRecord *cached_record)
// {
//     // Create DNS message structure
//     DNS_message response;

//     // Allocate and set up header
//     response.header = malloc(sizeof(DNS_header));
//     if (!response.header)
//         return -1;

//     response.header->transactionID = transactionID;
//     response.header->flags = htons(0x8180); // QR=1, AA=1, RD=1, RA=1, RCODE=0
//     response.header->ques_num = htons(1);   // 1 question
//     response.header->ans_num = htons(1);    // 1 answer
//     response.header->auth_num = 0;
//     response.header->add_num = 0;

//     // Allocate and set up question
//     response.question = malloc(sizeof(DNS_question));
//     if (!response.question)
//     {
//         free(response.header);
//         return -1;
//     }

//     response.question->qname = strdup(query_name);
//     response.question->qtype = htons(query_type);
//     response.question->qclass = htons(1); // IN class

//     // Allocate and set up answer
//     response.answer = malloc(sizeof(DNS_resource_record));
//     if (!response.answer)
//     {
//         free(response.header);
//         free(response.question->qname);
//         free(response.question);
//         return -1;
//     }

//     response.answer->name = strdup(query_name);
//     response.answer->type = htons(query_type);
//     response.answer->class = htons(1); // IN class

//     // Calculate remaining TTL (expire_time - current_time)
//     time_t current_time = time(NULL);
//     uint32_t ttl = (cached_record->expire_time > current_time) ? (cached_record->expire_time - current_time) : 0;
//     response.answer->ttl = htonl(ttl);

//     // 设置answer字段
//     if (query_type == RR_A)
//     { // A record
//         response.answer->rdlength = htons(4);
//         printf("cached record %d\n", cached_record->ip_addr.addr.ipv4);
//         memcpy(response.answer->data.a_record.IP_addr, &cached_record->ip_addr.addr.ipv4, 4);
//     }
//     else if (query_type == RR_AAAA)
//     { // AAAA record
//         response.answer->rdlength = htons(16);
//         memcpy(response.answer->data.aaaa_record.IP_addr, &cached_record->ip_addr, 16);
//     }
//     else if (query_type == RR_CNAME)
//     { // CNAME record
//         // Assuming domain field contains the CNAME target
//         response.answer->rdlength = htons(strlen(cached_record->domain) + 1);
//         response.answer->data.cname_record.name = strdup(cached_record->domain);
//     }
//     else
//     {
//         // Unsupported record type
//         free(response.header);
//         free(response.question->qname);
//         free(response.question);
//         free(response.answer->name);
//         free(response.answer);
//         return -1;
//     }

//     // Convert DNS_message to wire format
//     int response_len = dns_message_to_wire(&response, buffer, buf_size);

//     // Free allocated memory
//     free(response.header);
//     free(response.question->qname);
//     free(response.question);
//     free(response.answer->name);
//     if (query_type == RR_CNAME)
//     {
//         free(response.answer->data.cname_record.name);
//     }
//     free(response.answer);

//     return response_len;
// }

// 构建dns响应报文
int build_dns_response(unsigned char *request, int requestLen, const char *ip)
{
    // 假设你已经解析出请求中的 ID、问题部分等内容
    // 这里只是简单构造一个响应示意，不完整！

    unsigned char *response = request; // 直接在原缓冲区构造
    // 设置响应标志位
    response[2] |= 0x80; // QR = 1 表示响应
    response[3] = 0x80;  // 递归可用

    response[7] = 0x01; // ANCOUNT = 1，回答数量为1

    int offset = requestLen; // 在原请求基础上添加

    // 构造回答部分（压缩名指针）
    response[offset++] = 0xC0;
    response[offset++] = 0x0C; // Name 指针指向问题部分

    response[offset++] = 0x00;
    response[offset++] = 0x01; // Type A
    response[offset++] = 0x00;
    response[offset++] = 0x01; // Class IN
    response[offset++] = 0x00;
    response[offset++] = 0x00;
    response[offset++] = 0x00;
    response[offset++] = 0x3C; // TTL = 60s
    response[offset++] = 0x00;
    response[offset++] = 0x04; // RDLENGTH = 4

    // IP 转为4字节写入
    unsigned char ipParts[4];
    sscanf(ip, "%hhu.%hhu.%hhu.%hhu", &ipParts[0], &ipParts[1], &ipParts[2], &ipParts[3]);
    memcpy(response + offset, ipParts, 4);
    offset += 4;

    return offset;
}
