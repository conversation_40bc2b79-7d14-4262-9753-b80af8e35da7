#pragma once

#include "../inc/default.h"
#include "../inc/header.h"

/*
 * 域名拦截表
 * 使用简单的数组结构存储需要拦截的域名
 * 采用零地址方法：拦截的域名对应IP地址0.0.0.0
 */

#define MAX_BLACKLIST_SIZE 1000  // 最大拦截域名数量

typedef struct BlacklistEntry {
    char domain[DOMAIN_MAX_LEN];  // 被拦截的域名
    time_t added_time;            // 添加到拦截表的时间
} BlacklistEntry;

typedef struct DomainBlacklist {
    BlacklistEntry entries[MAX_BLACKLIST_SIZE];
    int count;  // 当前拦截域名数量
} DomainBlacklist;

// 全局拦截表
extern DomainBlacklist* domain_blacklist;

// 函数声明
DomainBlacklist* init_blacklist();
int add_to_blacklist(DomainBlacklist* blacklist, const char* domain);
int is_domain_blocked(DomainBlacklist* blacklist, const char* domain);
int remove_from_blacklist(DomainBlacklist* blacklist, const char* domain);
void print_blacklist(DomainBlacklist* blacklist);
void cleanup_blacklist(DomainBlacklist* blacklist);