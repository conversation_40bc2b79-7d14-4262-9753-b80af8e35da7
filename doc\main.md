```c
int main() {
    解析命令行（调试信息）

    打印项目信息：print_project_info()

    // 程序开始

    初始化socket：init_socket(PORT)

    初始化DNS缓存：init_DNS()

    进入轮询函数：poll()

    // 程序结束

    关闭socket：closesocket(sock)

    清理WSA资源：WSACleanup()
}
```

```c
void init_socket(int 端口号port) {
    初始化WSA

    创建客户端socket和服务器socket：socket()
    初始化client_sock, server_sock

    初始化client_addr, server_addr

    设置端口复用

    绑定端口：bind(client_sock, (SOCKADDR*)&client_addr, addr_len)

    打印运行信息
}
```

```c
void init_DNS(void) {
    初始化DNS缓存：init_dns_cache(500)（容量500）

    初始化拦截表：init_blacklist()

    加载host文件到缓存和拦截表：init_hosts_to_dns_cache()
}
```

```c
void poll(void) {
    设置非阻塞

    声明pollfd，监视两个socket

    主循环接收DNS请求：while(1) {
        调用WSAPoll等待：WSAPoll(fds,2,5)

        错误处理;

        if(client_sock有数据到达) {
            调用处理函数：receiveClient()
        }

        if(server_sock有数据到达) {
            调用处理函数：receiveServer()
        }
    }
}
```

```c
void receiveClient(void) {
    声明一个存放DNS报文的结构体：DNS_message msg（主机字节序）

    调用接收函数：recvfrom(client_sock, buffer, BUF_SIZE, 0, (struct sockaddr*)&clientAddr, &addr_len)
    接收到buffer中

    解析DNS报文：parse_dns_packet(&msg, buffer, recvLen)
    从buffer中拿下来，解析后存入msg（主机字节序）

    // 检查缓存

    先保存msg中的请求名称query_name和请求类型query_type

    保存事务ID到client_txid

    保存客户端地址到original_client以便后续回复

    // 先检查是否在黑名单中

    if(如果该名称被拦截：is_domain_blocked(domain_blacklist, query_name)) {
        构造NXDOMAIN响应：build_nxdomain_response(buffer, BUF_SIZE, client_txid, query_name, query_type)

        发送响应给客户端：sendto(client_sock, buffer, response_len, 0, (struct sockaddr*)&original_client, addr_len)
    }

    // 再从缓存中查找

    打印缓存状态（调试用）：print_cache_status(dns_cache)

    声明一个存放缓存信息的结构体：DNSRecord* cached_record = NULL

    标记是否是临时链表（使用别名查询会用到）：int is_temp_chain = 0

    if(请求类型为CNAME) {

        调用缓存查询函数，结果存放在cached_record：
        cached_record = query_cache_all_records(dns_cache, query_name, RR_CNAME);

    } else（请求类型为A或AAAA） {

        调用缓存查询函数，结果存放在cached_record：
        cached_record = query_cache_all_records(dns_cache, query_name, msg.question->qtype);

        if(没有直接的A/AAAA记录!cached_record) {

            尝试收集完整的CNAME链：
            cached_record = collect_cname_chain_with_records(dns_cache, query_name, msg.question->qtype)
            标记为临时链表：is_temp_chain = 1

        }
    }

    // 如果缓存命中

    if(缓存命中：cached_record != NULL) {

        调用多记录（适配一个域名多个IP的情况）响应构造函数：
        build_multi_record_response(buffer, BUF_SIZE, client_txid, query_name, query_type, cached_record)

        if(是CNAME或者A查询) {
            打印字节数据（调试用）
        }

        发送缓存响应给客户端：sendto(client_sock, buffer, response_len, 0, (struct sockaddr*)&original_client, addr_len)

        if(使用了临时链表：is_temp_chain) {
            释放：free_temp_record_chain(cached_record)
        }

    } else（缓存未命中） {

        // 转发给上游客户端，处理了多并发问题（ID转换）

        查找空闲槽位：find_free_slot()

        if(没有空闲槽位（并发极限1024）：slot == -1) {
            报错返回
        }

        保存事务ID和客户端信息：
        保存原始ID（收到上游回复后找到请求的客户端）：ID_list[slot].orig_id = client_txid
        保存客户端地址（用于转发上游回复的地址）：ID_list[slot].cli = original_client
        保存时间戳（超时处理）：ID_list[slot].timestamp = time(NULL)
        标记该槽位已用：ID_used[slot] = true

        修改事务ID为槽位索引+1（避免冲突）：new_txid = slot + 1
        写入buffer（转换成网络字节序）：
        buffer[0] = (new_txid >> 8) & 0xFF
        buffer[1] = new_txid & 0xFF

        发送请求到上游DNS服务器：
         sendto(server_sock, buffer, recvLen, 0, (struct sockaddr*)&server_addr, sizeof(server_addr))
    }
}
```

```c
void receiveServer(void) {
    接收上游DNS发来的报文：recvfrom(server_sock, buffer, BUF_SIZE, 0, (struct sockaddr*)&server_addr, &remote_addr_len)

    获取事务ID（转换到主机字节序）：server_txid = (buffer[0] << 8) | buffer[1]

    if(事务ID无效：server_txid == 0 || server_txid > MAX_INFLIGHT) {
        报错返回
    }

    （若有效）事务ID转换为槽位索引：slot = server_txid - 1

    if(这个槽位并未使用：!ID_used[slot]) {
        报错返回
    }

    恢复原始事务ID：orig_txid = ID_list[slot].orig_id
    写入buffer（转换成网络字节序）：
    buffer[0] = (orig_txid >> 8) & 0xFF
    buffer[1] = orig_txid & 0xFF

    获取原始客户端地址：sockaddr_in original_client = ID_list[slot].cli

    声明一个存放DNS报文的结构体：DNS_message response_msg

    解析DNS报文：parse_dns_packet(&response_msg, buffer, remote_recvLen)
    从buffer中解析并写入response_msg

    获取查询名称：query_name = response_msg.question[0].qname

    // 缓存上游DNS服务器的响应（Answer段）

    if(有效) {
        for(遍历所有回答：int i = 0; i < response_msg.header->ans_num; i++) {

            声明一个存放DNS资源记录的结构体，存放回答：
            DNS_resource_record* rr = &response_msg.answer[i]

            if(如果存的是A类型：rr->type == RR_A) {

                声明一个整型存放IPv4地址：uint32_t ipv4_addr
                存放A记录IP地址：memcpy(&ipv4_addr, rr->data.a_record.IP_addr, 4)
                添加到缓存中：add_to_cache(dns_cache, rr->name, RR_A, &ipv4_addr, rr->ttl)

            } else if(存的是AAAA类型：rr->type == RR_AAAA) {

                声明一个整型数组存放IPv6地址：uint8_t ipv6_addr[16]
                存放AAAA记录IP地址：memcpy(&ipv6_addr, rr->data.aaaa_record.IP_addr, 16)
                添加到缓存中：add_to_cache(dns_cache, rr->name, RR_AAAA, &ipv6_addr, rr->ttl)

            } else if(存的是CNAME类型：rr->type == RR_CNAME) {

                添加到缓存中：add_to_cache(dns_cache, rr->name, RR_CNAME, rr->data.cname_record.name, rr->ttl)

            }
        }
    }

    // 缓存上游DNS服务器的响应（Additional段）
    if(有效) {

        for(遍历所有add：int i = 0; i < response_msg.header->add_num; i++) {

            声明一个存放DNS资源记录的结构体，存放附加内容
            DNS_resource_record* rr = &response_msg.additional[i]

            if(如果是A类型) {

                处理方式与Answer段类似

            } else if(如果是AAAA类型) {

                处理方式与Answer段类似

            } else if(如果是CNAME类型) {

                处理方式与Answer段类似

            }

        }
    }

    将响应返回给原始客户端：sendto(client_sock, buffer, remote_recvLen, 0, (struct sockaddr*)&original_client, addr_len)

    释放槽位：ID_used[slot] = false
}
```

```c
/*
 * 构建DNS NXDOMAIN响应（域名不存在错误）
 * 用于拦截域名时返回给客户端
 */
int build_nxdomain_response(unsigned char* buffer, int buf_size, uint16_t transactionID, const char* query_name, uint16_t query_type) {
    错误处理（没有buffer || 没有查询名称 || buffer不够大）

    声明并设置偏移量为0：int offset = 0

    // DNS头

    设置事务ID（主机字节序转网络字节序）：
    buffer[0] = (transactionID >> 8) & 0xFF;  // Transaction ID high byte
    buffer[1] = transactionID & 0xFF;         // Transaction ID low byte
    offset += 2;

    // Flags: QR=1(response), OPCODE=0(query), AA=0, TC=0, RD=1, RA=1, Z=000, RCODE=3(NXDOMAIN)
    设置Flags：
    uint16_t flags = 0x8183;  // 1000 0001 1000 0011
    buffer[2] = (flags >> 8) & 0xFF;
    buffer[3] = flags & 0xFF;
    offset += 2;
    标志位解读：这是一个对标准查询的响应。我不是权威服务器，但我支持递归。我正在响应你那个期望递归的请求。这个响应是完整的，没有被截断。然而，经过查询，你所问的那个域名根本不存在 (NXDomain)

    // Questions: 1
    设置Question：
    buffer[4] = 0x00;
    buffer[5] = 0x01;
    offset += 2;

    // Answer RRs: 0
    设置Answer：
    buffer[6] = 0x00;
    buffer[7] = 0x00;
    offset += 2;

    // Authority RRs: 0
    设置Authority：
    buffer[8] = 0x00;
    buffer[9] = 0x00;
    offset += 2;

    // Additional RRs: 0
    设置Additional：
    buffer[10] = 0x00;
    buffer[11] = 0x00;
    offset += 2;

    // Question section
    编码域名放入buffer

    // QTYPE
    buffer[offset] = (query_type >> 8) & 0xFF;
    buffer[offset + 1] = query_type & 0xFF;
    offset += 2;

    // QCLASS (IN = 1)
    buffer[offset] = 0x00;
    buffer[offset + 1] = 0x01;
    offset += 2;

    返回偏移量：return offset
}
```
