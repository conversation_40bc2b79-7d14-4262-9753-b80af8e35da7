#pragma once

#include "../inc/default.h"
#include "../inc/header.h"

/*
trie树大致样貌
1. www.example.com
2. api.example.com

[root] (TrieNode)
|
└── m (is_end=0)  // 注意：后缀表示法从右向左构建
    └── o
        └── c
            └── .
                └── e
                    └── l
                        └── p
                            └── m
                                └── a
                                    └── x
                                        └── e
                                            └── .
                                                ├── w (is_end=0)
                                                │   └── w
                                                │       └── w (is_end=1, record→"www.example.com")
                                                └── i (is_end=0)
                                                    └── p
                                                        └── a (is_end=1, record→"api.example.com")


*/

typedef struct ip_address
{
    uint8_t type;
    union
    {
        uint32_t ipv4;    // IPv4 地址
        uint8_t ipv6[16]; // IPv6 地址
    } addr;
} IPAdress;

typedef struct node
{
    uint16_t type;
    char domain[DOMAIN_MAX_LEN]; // 域名

    union {
        IPAdress ip_addr;                // IP地址 (A/AAAA记录)
        char cname[DOMAIN_MAX_LEN];      // CNAME目标域名 (CNAME记录)
    } data;

    time_t expire_time; // 过期时间戳
    time_t last_used;   // 最后使用时间(LRU用)

    struct node *next;                // 指向下一个节点
    struct node *lru_prev, *lru_next; // 指向上一个节点

} DNSRecord;

/*
 * Trie树节点结构体
 * 用于高效域名前缀匹配和查找
 * 每个节点包含36个子节点（a-z, 0-9）和一个指向实际记录的指针
 * is_end标志表示是否是一个完整的域名结尾
 * record指针指向实际的DNS记录
 * 这个里面每一个节点有一个字符组成，叶子节点存储的是完整的域名记录
 */
typedef struct TireNode
{
    struct TireNode *children[36]; // a-z, 0-9
    DNSRecord *record;             // 指向实际记录
    int is_end;                    // 是否是域名结尾
} TrieNode;

/*
 * DNS缓存系统核心结构体
 * 结合Trie树和LRU算法实现高效缓存管理
 */
typedef struct DNSCache
{
    TrieNode *root;      // Trie树根节点
    DNSRecord *lru_head; // LRU链表头(最近使用)
    DNSRecord *lru_tail; // LRU链表尾(最久未使用)
    int capacity;        // 缓存容量
    int size;            // 当前大小
} DNSCache;

DNSCache *dns_cache;

TrieNode *create_trie_node();
DNSCache *init_dns_cache(int capacity);
void normalize_domain(char *domain);
void trie_insert(DNSCache *cache, DNSRecord *record);
DNSRecord *trie_search(DNSCache *cache, const char *domain);
void lru_update(DNSCache *cache, DNSRecord *record);
void lru_evict(DNSCache *cache);
void add_to_cache(DNSCache *cache, const char *domain, int addr_type, void *addr, time_t ttl);
DNSRecord *query_cache(DNSCache *cache, const char *domain);
DNSRecord *query_cache_all_records(DNSCache *cache, const char *domain, uint16_t type);
void print_cache_status(DNSCache *cache);

void init_DNS();