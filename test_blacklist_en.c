#include "inc/blacklist.h"
#include "inc/LRU.h"
#include "inc/server.h"

int main() {
    printf("=== Domain Blacklist Function Test ===\n\n");
    
    // Initialize DNS system
    init_DNS();
    
    printf("\n=== Testing Domain Blacklist Function ===\n");
    
    // Test domain list
    const char* test_domains[] = {
        "facebook.com",           // In blacklist
        "ad.doubleclick.net",     // In blacklist  
        "google.com",             // Not in blacklist
        "example.com",            // Not in blacklist, but in cache
        "malware.example.com"     // In blacklist
    };
    
    int num_tests = sizeof(test_domains) / sizeof(test_domains[0]);
    
    for (int i = 0; i < num_tests; i++) {
        printf("\n--- Testing domain: %s ---\n", test_domains[i]);
        
        // Check if in blacklist
        if (is_domain_blocked(domain_blacklist, test_domains[i])) {
            printf("Result: Domain is BLOCKED, will return NXDOMAIN response\n");
        } else {
            printf("Result: Domain is NOT blocked, checking cache...\n");
            
            // Check cache
            DNSRecord* record = query_cache(dns_cache, test_domains[i]);
            if (record) {
                printf("Cache hit: domain %s", test_domains[i]);
                if (record->type == RR_A) {
                    uint32_t ip = record->data.ip_addr.addr.ipv4;
                    printf(" -> %d.%d.%d.%d\n", 
                        (ip & 0xFF), 
                        (ip >> 8) & 0xFF,
                        (ip >> 16) & 0xFF, 
                        (ip >> 24) & 0xFF);
                } else {
                    printf(" (non-A record)\n");
                }
            } else {
                printf("Cache miss, need to forward to remote DNS server\n");
            }
        }
    }
    
    printf("\n=== Testing Add/Remove Blacklist Domains ===\n");
    
    // Test adding new blocked domain
    printf("Adding new blocked domain: malicious.com\n");
    add_to_blacklist(domain_blacklist, "malicious.com");
    
    // Test removing blocked domain
    printf("Removing blocked domain: facebook.com\n");
    remove_from_blacklist(domain_blacklist, "facebook.com");
    
    // Show updated blacklist
    print_blacklist(domain_blacklist);
    
    // Test the removed domain again
    printf("Testing facebook.com again (should no longer be blocked):\n");
    if (is_domain_blocked(domain_blacklist, "facebook.com")) {
        printf("Still blocked\n");
    } else {
        printf("No longer blocked\n");
    }
    
    // Cleanup resources
    cleanup_blacklist(domain_blacklist);
    
    printf("\n=== Test Complete ===\n");
    return 0;
} 