#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <winsock2.h>
#pragma comment(lib, "ws2_32.lib")

#define DNS_SERVER "********"
#define DNS_PORT 53
#define BUF_SIZE 512

// 构造 DNS 报文中的 QNAME
int encode_qname(char *buf, const char *host)
{
    int len = 0;
    const char *pos = host;
    while (*pos)
    {
        const char *dot = strchr(pos, '.');
        if (!dot)
            dot = pos + strlen(pos);

        int label_len = dot - pos;
        buf[len++] = label_len;
        memcpy(buf + len, pos, label_len);
        len += label_len;

        if (*dot == '.')
            pos = dot + 1;
        else
            break;
    }
    buf[len++] = 0x00; // QNAME 结尾
    return len;
}

// 构造一个 DNS 查询请求
int build_dns_query(char *buf, const char *host)
{
    memset(buf, 0, BUF_SIZE);
    // Transaction ID
    buf[0] = 0x12;
    buf[1] = 0x34;
    // Flags: 标准查询，递归
    buf[2] = 0x01;
    buf[3] = 0x00;
    // Questions = 1
    buf[4] = 0x00;
    buf[5] = 0x01;
    // Answer/Authority/Additional = 0

    int offset = 12;
    offset += encode_qname(buf + offset, host);

    // QTYPE = A
    buf[offset++] = 0x00;
    buf[offset++] = 0x01;
    // QCLASS = IN
    buf[offset++] = 0x00;
    buf[offset++] = 0x01;

    return offset; // 总长度
}

// 打印响应信息（含回答记录）
void parse_dns_response(char *buf, int len)
{
    if (len < 12)
        return;
    unsigned short id = (buf[0] << 8) | buf[1];
    unsigned short flags = (buf[2] << 8) | buf[3];
    unsigned short qdcount = (buf[4] << 8) | buf[5];
    unsigned short ancount = (buf[6] << 8) | buf[7];

    printf("Transaction ID: 0x%04X\n", id);
    printf("Flags: 0x%04X (QR=%d)\n", flags, (flags >> 15) & 1);
    printf("Questions: %d, Answers: %d\n", qdcount, ancount);

    int offset = 12;

    // 跳过 QNAME
    while (buf[offset] != 0 && offset < len)
        offset += buf[offset] + 1;
    offset += 5; // 跳过 0x00 + QTYPE + QCLASS

    for (int i = 0; i < ancount; ++i)
    {
        if (offset + 12 > len)
            break;
        // name(2) + type(2) + class(2) + ttl(4) + rdlength(2)
        offset += 2; // name (压缩指针)
        unsigned short type = (buf[offset] << 8) | buf[offset + 1];
        unsigned short rdlen = (buf[offset + 8] << 8) | buf[offset + 9];
        offset += 10;

        if (type == 1 && rdlen == 4 && offset + 4 <= len)
        {
            printf("A record: %u.%u.%u.%u\n",
                   (unsigned char)buf[offset],
                   (unsigned char)buf[offset + 1],
                   (unsigned char)buf[offset + 2],
                   (unsigned char)buf[offset + 3]);
        }
        offset += rdlen;
    }
}

int main()
{
    WSADATA wsaData;
    SOCKET sock;
    struct sockaddr_in server;
    char sendbuf[BUF_SIZE], recvbuf[BUF_SIZE];

    WSAStartup(MAKEWORD(2, 2), &wsaData);
    sock = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);

    server.sin_family = AF_INET;
    server.sin_port = htons(DNS_PORT);
    server.sin_addr.s_addr = inet_addr(DNS_SERVER);

    const char *domain = "www.baidu.com";
    int query_len = build_dns_query(sendbuf, domain);

    sendto(sock, sendbuf, query_len, 0, (struct sockaddr *)&server, sizeof(server));

    int server_len = sizeof(server);
    int recv_len = recvfrom(sock, recvbuf, BUF_SIZE, 0, (struct sockaddr *)&server, &server_len);

    if (recv_len > 0)
    {
        printf("Received DNS response (%d bytes)\n", recv_len);
        parse_dns_response(recvbuf, recv_len);
    }

    closesocket(sock);
    WSACleanup();
    return 0;
}
