#include "inc/host.h"
#include "inc/LRU.h"
#include "inc/blacklist.h"

int main() {
    printf("=== Testing Host File DNS Initialization ===\n\n");
    
    // 初始化DNS系统
    init_DNS();
    
    printf("\n=== Testing DNS Cache Queries ===\n");
    
    // 测试查询一些域名
    const char* test_domains[] = {
        "google.com",
        "www.google.com", 
        "dns.google.com",
        "malware.example.com",  // 应该被拦截
        "server.local",
        "ipv6.google.com",
        "nonexistent.domain.com"
    };
    
    int num_tests = sizeof(test_domains) / sizeof(test_domains[0]);
    
    for (int i = 0; i < num_tests; i++) {
        printf("\nQuerying: %s\n", test_domains[i]);
        
        // 检查是否在拦截表中
        if (is_domain_blocked(domain_blacklist, test_domains[i])) {
            printf("  -> BLOCKED (in blacklist)\n");
            continue;
        }
        
        // 查询 A 记录
        DNSRecord* record_a = query_cache_all_records(dns_cache, test_domains[i], RR_A);
        if (record_a) {
            uint32_t ip = record_a->data.ip_addr.addr.ipv4;
            printf("  -> A: %u.%u.%u.%u\n", 
                   (ip >> 24) & 0xFF, (ip >> 16) & 0xFF, 
                   (ip >> 8) & 0xFF, ip & 0xFF);
        }
        
        // 查询 AAAA 记录
        DNSRecord* record_aaaa = query_cache_all_records(dns_cache, test_domains[i], RR_AAAA);
        if (record_aaaa) {
            uint8_t* ipv6 = record_aaaa->data.ip_addr.addr.ipv6;
            printf("  -> AAAA: %02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x\n",
                   ipv6[0], ipv6[1], ipv6[2], ipv6[3], ipv6[4], ipv6[5], ipv6[6], ipv6[7],
                   ipv6[8], ipv6[9], ipv6[10], ipv6[11], ipv6[12], ipv6[13], ipv6[14], ipv6[15]);
        }
        
        if (!record_a && !record_aaaa) {
            printf("  -> Not found in cache\n");
        }
    }
    
    printf("\n=== Final Statistics ===\n");
    HostsStats stats = get_hosts_stats();
    print_hosts_stats(stats);
    
    printf("=== Test Completed ===\n");
    return 0;
} 