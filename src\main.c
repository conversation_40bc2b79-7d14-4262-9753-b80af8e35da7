#include "../inc/LRU.h"
#include "../inc/dnsStruct.h"
#include "../inc/header.h"
#include "../inc/log.h"
#include "../inc/server.h"

int main(int argc, char* argv[]) {
    // 解析命令行
    for (int i = 1; i < argc; i++) {
        if (!strcmp(argv[i], "-d")) {
            log_level = LOG_LEVEL_INFO;
        } else if (!strcmp(argv[i], "-dd")) {
            log_level = LOG_LEVEL_DEBUG;
        } else if (!strcmp(argv[i], "-ddd")) {
            log_level = LOG_LEVEL_BYTE;
        }
    }

    if (log_level > LOG_LEVEL_NONE) {  // 日志初始化
        log_init("dnsserver.log");
        LOG_INFO("Log level = %d\n", log_level);
    }

    print_project_info();

    init_socket(PORT);

    /*
    初始化DNS，初始化拦截表，拦截表在init_DNS当中的domain_blacklist = init_blacklist();当中，
    在init_block当中设置需要拦截的域名和IP地址是什么
    */
    init_DNS();  // 初始化DNS缓存，容量为500

    poll();

    closesocket(sock);
    WSACleanup();
    return 0;
}
