#include "../inc/server.h"
#include "../inc/LRU.h"
#include "../inc/blacklist.h"
#include "../inc/dnsStruct.h"
#include "../inc/log.h"

int addr_len = sizeof(struct sockaddr_in);
char *remote_dns = "10.3.9.6";
// char* remote_dns = "192.168.1.1";

void init_socket(int port)
{
    // 初始化，否则无法运行socket
    WORD wVersion = MAKEWORD(2, 2);
    WSADATA wsadata;
    if (WSAStartup(wVersion, &wsadata) != 0)
    {
        return;
    }

    client_sock = socket(AF_INET, SOCK_DGRAM, 0);
    server_sock = socket(AF_INET, SOCK_DGRAM, 0);

    // 初始化两个结构体以留空
    memset(&client_addr, 0, sizeof(client_addr));
    memset(&server_addr, 0, sizeof(server_addr));

    client_addr.sin_family = AF_INET;
    client_addr.sin_addr.s_addr = INADDR_ANY; // INADDR_ANY表示本机的任意IP地址
    client_addr.sin_port = htons(port);

    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = inet_addr(remote_dns); // 远程主机地址
    server_addr.sin_port = htons(port);

    // 端口复用
    const int REUSE = 1;
    setsockopt(client_sock, SOL_SOCKET, SO_REUSEADDR, (const char *)&REUSE, sizeof(REUSE));

    if (bind(client_sock, (SOCKADDR *)&client_addr, addr_len) < 0)
    {
        printf("ERROR: Could not bind: %s\n", strerror(errno));
        exit(-1);
    }

    char *DNS_server = remote_dns;
    printf("======================= DNS server running =======================\n");
    printf("| DNS server: %-12s                                       |\n", DNS_server);
    printf("| Listening on port %-12d                                 |\n", port);
    printf("==================================================================\n");
}

// 轮询函数：非阻塞监听 client_sock 和 server_sock 的输入事件
void poll(void)
{
    u_long block_mode = 1; // 1 表示设置为非阻塞模式
    int server_result = ioctlsocket(server_sock, FIONBIO, &block_mode);
    int client_result = ioctlsocket(client_sock, FIONBIO, &block_mode);

    // 检查是否设置非阻塞失败
    if (server_result == SOCKET_ERROR || client_result == SOCKET_ERROR)
    {
        // 设置失败
        printf("ioctlsocket failed with error: %d\n", WSAGetLastError());
        closesocket(server_sock);
        closesocket(client_sock);
        WSACleanup();
        return;
    }

    // 声明一个 pollfd 数组，用于同时监视两个 SOCKET：client_sock 和 server_sock
    struct pollfd fds[2];

    // 循环接收 DNS 请求
    while (1)
    {
        // 设置 fds[0]，用于监视"客户端 UDP socket"是否有数据可读
        fds[0].fd = client_sock; // client_sock 是之前用 socket() 创建并 bind 到 53 端口的 UDP socket
        fds[0].events = POLLIN;  // 我们只关心"可读"事件：如果客户端发来一个 DNS 查询报文，socket 可读时触发
        // 设置 fds[1]，用于监视"上游 DNS UDP socket"是否有数据可读
        fds[1].fd = server_sock; // server_sock 是另一个 UDP socket，用于向上游 DNS发起转发请求
        fds[1].events = POLLIN;  // 只关心"可读"：上游 DNS 返回响应时，我们要读它送回的查询结果

        // 调用 WSAPoll 进行等待
        int ret = WSAPoll(fds, // fds = 上面填写好的 pollfd 数组
                          2,   // 2 = 数组长度，共两个元素：一个监视客户端，一个监视上游 DNS
                          5    // 5 = 超时时间：5 毫秒。如果 5ms 内没有任何一个 socket 可读，就返回 0
        );

        // 判断 WSAPoll 的返回情况
        if (ret == SOCKET_ERROR)
        {
            // 如果返回 SOCKET_ERROR，说明 WSAPoll 调用失败
            // 这很可能是 Winsock 尚未正确初始化、socket 句柄非法，或者其它系统/资源问题
            printf("ERROR WSAPoll: %d.\n", WSAGetLastError());
            LOG_INFO("ERROR WSAPoll: %d.\n", WSAGetLastError());
        }
        else if (ret > 0)
        {
            // 有事件发生，判断是哪个 socket 有数据
            if (fds[0].revents & POLLIN)
            {
                // client_sock 有数据到达，处理客户端请求
                receiveClient();
            }
            if (fds[1].revents & POLLIN)
            {
                // server_sock 有数据到达，处理服务器响应
                receiveServer();
            }
        }
    }
}

char *encode_dns_name(char *ptr, const char *name)
{
    const char *p = name;
    while (*p)
    {
        size_t len = 0;
        const char *dot = strchr(p, '.');
        if (dot)
        {
            len = dot - p;
        }
        else
        {
            len = strlen(p);
        }

        *ptr++ = (char)len;
        memcpy(ptr, p, len);
        ptr += len;

        if (dot)
        {
            p = dot + 1;
        }
        else
        {
            p += len;
        }
    }
    *ptr++ = '\0';
    return ptr;
}

int dns_message_to_wire(DNS_message *msg, unsigned char *buffer, int buf_size)
{
    if (!msg || !buffer || buf_size <= 0)
        return -1;

    int offset = 0;

    // Write header
    if (offset + 12 > buf_size)
        return -1;
    // memcpy(buffer + offset, &msg->header->transactionID, 2);
    buffer[0] = (msg->header->transactionID >> 8) & 0xFF; // 高字节
    buffer[1] = msg->header->transactionID & 0xFF;        // 低字节
    offset += 2;
    memcpy(buffer + offset, &msg->header->flags, 2);
    offset += 2;
    memcpy(buffer + offset, &msg->header->ques_num, 2);
    offset += 2;
    memcpy(buffer + offset, &msg->header->ans_num, 2);
    offset += 2;
    memcpy(buffer + offset, &msg->header->auth_num, 2);
    offset += 2;
    memcpy(buffer + offset, &msg->header->add_num, 2);
    offset += 2;

    // Write question (simplified - should handle name compression)
    if (msg->question)
    {
        // Write domain name (simple non-compressed format)
        const char *name = msg->question->qname;
        while (*name)
        {
            char *dot = strchr(name, '.');
            int len = dot ? (dot - name) : strlen(name);

            if (offset + len + 1 > buf_size)
                return -1;
            buffer[offset++] = len;
            memcpy(buffer + offset, name, len);
            offset += len;

            name = dot ? dot + 1 : name + len;
        }
        buffer[offset++] = 0; // Null terminator

        // Write QTYPE and QCLASS
        if (offset + 4 > buf_size)
            return -1;
        memcpy(buffer + offset, &msg->question->qtype, 2);
        offset += 2;
        memcpy(buffer + offset, &msg->question->qclass, 2);
        offset += 2;
    }

    // Write answer (simplified - should handle name compression)
    if (msg->answer)
    {
        // Write domain name (use pointer compression)
        if (offset + 2 > buf_size)
            return -1;
        buffer[offset++] = 0xC0; // Pointer flag
        buffer[offset++] = 0x0C; // Pointer to question name

        // Write TYPE, CLASS, TTL
        if (offset + 10 > buf_size)
            return -1;
        memcpy(buffer + offset, &msg->answer->type, 2);
        offset += 2;
        memcpy(buffer + offset, &msg->answer->class, 2);
        offset += 2;
        memcpy(buffer + offset, &msg->answer->ttl, 4);
        offset += 4;

        // Write RDLENGTH and RDATA
        if (msg->answer->type == htons(RR_A))
        {
            if (offset + 6 > buf_size)
                return -1;
            uint16_t rdlength = htons(4);
            memcpy(buffer + offset, &rdlength, 2);
            offset += 2;
            memcpy(buffer + offset, msg->answer->data.a_record.IP_addr, 4);
            offset += 4;
        }
        else if (msg->answer->type == htons(RR_AAAA))
        {
            if (offset + 18 > buf_size)
                return -1;
            uint16_t rdlength = htons(16);
            memcpy(buffer + offset, &rdlength, 2);
            offset += 2;
            memcpy(buffer + offset, msg->answer->data.aaaa_record.IP_addr, 16);
            offset += 16;
        }
        else if (msg->answer->type == htons(RR_CNAME))
        {
            // 计算CNAME域名的wire format长度
            const char *cname = msg->answer->data.cname_record.name;
            int name_wire_len = 0;
            const char *temp_name = cname;

            // 计算编码后的域名长度
            while (*temp_name)
            {
                char *dot = strchr(temp_name, '.');
                int label_len = dot ? (dot - temp_name) : strlen(temp_name);
                name_wire_len += label_len + 1; // +1 for length byte
                temp_name = dot ? dot + 1 : temp_name + label_len;
            }
            name_wire_len += 1; // +1 for null terminator

            // 写入RDLENGTH
            if (offset + 2 + name_wire_len > buf_size)
                return -1;
            uint16_t rdlength = htons(name_wire_len);
            memcpy(buffer + offset, &rdlength, 2);
            offset += 2;

            // 写入CNAME域名（DNS wire format）
            const char *name = cname;
            while (*name)
            {
                char *dot = strchr(name, '.');
                int len = dot ? (dot - name) : strlen(name);

                if (offset + len + 1 > buf_size)
                    return -1;
                buffer[offset++] = len;             // 标签长度
                memcpy(buffer + offset, name, len); // 标签内容
                offset += len;

                name = dot ? dot + 1 : name + len;
            }
            buffer[offset++] = 0; // 域名结束标志
        }
    }

    return offset;
}

/*
 * 根据缓存记录构建DNS响应消息，并将其转换为网络传输格式（wire format）
 *
 * 参数：
 *   buffer - 用于存储DNS消息网络格式的输出缓冲区
 *   buf_size - 输出缓冲区的大小
 *   transactionID - 响应中使用的DNS事务ID
 *   query_name - 查询的域名
 *   query_type - 查询的记录类型（A/AAAA/CNAME等）
 *   cached_record - 缓存中的DNS记录
 *
 * 返回值：
 *   成功时返回消息长度，失败返回-1
 */
int build_cached_response(unsigned char *buffer,
                          int buf_size,
                          uint16_t transactionID,
                          const char *query_name,
                          uint16_t query_type,
                          DNSRecord *cached_record)
{
    // Create DNS message structure
    DNS_message response;

    // Allocate and set up header
    response.header = malloc(sizeof(DNS_header));
    if (!response.header)
        return -1;

    response.header->transactionID = transactionID;
    response.header->flags = htons(0x8180); // QR=1, AA=1, RD=1, RA=1, RCODE=0
    response.header->ques_num = htons(1);   // 1 question
    response.header->ans_num = htons(1);    // 1 answer
    response.header->auth_num = 0;
    response.header->add_num = 0;

    // Allocate and set up question
    response.question = malloc(sizeof(DNS_question));
    if (!response.question)
    {
        free(response.header);
        return -1;
    }

    response.question->qname = strdup(query_name);
    response.question->qtype = htons(query_type);
    response.question->qclass = htons(1); // IN class

    // Allocate and set up answer
    response.answer = malloc(sizeof(DNS_resource_record));
    if (!response.answer)
    {
        free(response.header);
        free(response.question->qname);
        free(response.question);
        return -1;
    }

    response.answer->name = strdup(query_name);
    response.answer->type = htons(query_type);
    response.answer->class = htons(1); // IN class

    // Calculate remaining TTL (expire_time - current_time)
    time_t current_time = time(NULL);
    uint32_t ttl = (cached_record->expire_time > current_time) ? (cached_record->expire_time - current_time) : 0;
    response.answer->ttl = htonl(ttl);

    // 设置answer字段
    if (query_type == RR_A)
    { // A record
        response.answer->rdlength = htons(4);

        uint32_t ip_host_order = cached_record->data.ip_addr.addr.ipv4; // 这是主机字节序的IP
        uint32_t ip_network_order = htonl(ip_host_order);               // 转换为网络字节序以方便逐字节提取

        printf("Cached record (A): %u.%u.%u.%u (TTL: %lu)\n", (ip_network_order >> 24) & 0xFF, (ip_network_order >> 16) & 0xFF,
               (ip_network_order >> 8) & 0xFF, ip_network_order & 0xFF,
               ntohl(response.answer->ttl)); // TTL在response.answer中已经是网络字节序，打印时转回主机序更易读

        memcpy(response.answer->data.a_record.IP_addr, &cached_record->data.ip_addr.addr.ipv4, 4);
    }
    else if (query_type == RR_AAAA)
    { // AAAA record
        response.answer->rdlength = htons(16);
        memcpy(response.answer->data.aaaa_record.IP_addr, &cached_record->data.ip_addr.addr.ipv6, 16);
    }
    else if (query_type == RR_CNAME)
    { // CNAME record
        printf("=== Building CNAME Response ===\n");
        printf("Query name: %s\n", query_name);
        printf("Cached record domain: %s\n", cached_record->domain);
        printf("Cached record CNAME target: %s\n", cached_record->data.cname);
        printf("Record type in cache: %d\n", cached_record->type);

        // 使用缓存中的CNAME目标
        response.answer->data.cname_record.name = strdup(cached_record->data.cname);

        // 计算CNAME域名的wire format长度
        const char *cname = cached_record->data.cname;
        int name_wire_len = 0;
        const char *temp_name = cname;

        // 计算编码后的域名长度
        while (*temp_name)
        {
            char *dot = strchr(temp_name, '.');
            int label_len = dot ? (dot - temp_name) : strlen(temp_name);
            name_wire_len += label_len + 1; // +1 for length byte
            temp_name = dot ? dot + 1 : temp_name + label_len;
        }
        name_wire_len += 1; // +1 for null terminator

        response.answer->rdlength = htons(name_wire_len);

        printf("Setting CNAME target to: %s\n", cached_record->data.cname);
        printf("Setting rdlength to wire format length: %d\n", name_wire_len);
        printf("=============================\n");
    }
    else
    {
        // Unsupported record type
        free(response.header);
        free(response.question->qname);
        free(response.question);
        free(response.answer->name);
        free(response.answer);
        return -1;
    }

    // Convert DNS_message to wire format
    int response_len = dns_message_to_wire(&response, buffer, buf_size);

    // Free allocated memory
    free(response.header);
    free(response.question->qname);
    free(response.question);
    free(response.answer->name);
    if (query_type == RR_CNAME)
    {
        free(response.answer->data.cname_record.name);
    }
    free(response.answer);

    return response_len;
}

/**
 * 构建包含多个相同类型记录的DNS响应（直接生成wire format）
 *
 * 参数：
 *   buffer - 输出缓冲区
 *   buf_size - 缓冲区大小
 *   transactionID - 事务ID
 *   query_name - 查询域名
 *   query_type - 查询类型
 *   first_record - 第一个记录（链表头）
 *
 * 返回值：
 *   成功时返回消息长度，失败返回-1
 */
int build_multi_record_response(unsigned char *buffer,
                                int buf_size,
                                uint16_t transactionID,
                                const char *query_name,
                                uint16_t query_type,
                                DNSRecord *first_record)
{
    if (!buffer || !query_name || !first_record)
    {
        return -1;
    }

    int offset = 0;

    // 计算所有记录的数量（包括CNAME记录）
    int answer_count = 0;
    DNSRecord *current = first_record;
    while (current)
    {
        // 当查询A/AAAA记录时，如果有CNAME链，需要包含CNAME记录和最终的A/AAAA记录
        if (current->type == query_type || ((query_type == RR_A || query_type == RR_AAAA) && current->type == RR_CNAME))
        {
            answer_count++;
        }
        current = current->next;
    }

    if (answer_count == 0)
    {
        return -1;
    }

    printf("Building multi-record response for %s (type=%d), found %d records\n", query_name, query_type, answer_count);

    // 1. 写入DNS头部 (12字节)
    if (offset + 12 > buf_size)
        return -1;

    // 这里通过程序的方式改变字节序
    buffer[offset++] = (transactionID >> 8) & 0xFF;
    buffer[offset++] = transactionID & 0xFF;

    uint16_t flags = htons(0x8180); // QR=1, AA=1, RD=1, RA=1, RCODE=0
    memcpy(buffer + offset, &flags, 2);
    offset += 2;

    uint16_t ques_num = htons(1);
    memcpy(buffer + offset, &ques_num, 2);
    offset += 2;

    uint16_t ans_num = htons(answer_count);
    memcpy(buffer + offset, &ans_num, 2);
    offset += 2;

    uint16_t auth_num = 0, add_num = 0;
    memcpy(buffer + offset, &auth_num, 2);
    offset += 2;
    memcpy(buffer + offset, &add_num, 2);
    offset += 2;

    // 2. 写入问题部分
    const char *name = query_name;
    while (*name)
    {
        char *dot = strchr(name, '.');
        int len = dot ? (dot - name) : strlen(name);

        if (offset + len + 1 > buf_size)
            return -1;
        buffer[offset++] = len;
        memcpy(buffer + offset, name, len);
        offset += len;

        name = dot ? dot + 1 : name + len;
    }
    buffer[offset++] = 0; // 域名结束

    if (offset + 4 > buf_size)
        return -1;
    uint16_t qtype = htons(query_type);
    uint16_t qclass = htons(1);
    // 存放question当中的type和class
    // 注意：这里的qtype和qclass是网络字节序
    memcpy(buffer + offset, &qtype, 2);
    offset += 2;
    memcpy(buffer + offset, &qclass, 2);
    offset += 2;

    // 3. 写入答案部分（多个记录）
    current = first_record;
    while (current)
    {
        // 当查询A/AAAA记录时，包含CNAME记录和最终的A/AAAA记录
        if (current->type == query_type || ((query_type == RR_A || query_type == RR_AAAA) && current->type == RR_CNAME))
        {
            // 写入域名
            // 对于第一个记录（查询的域名），使用指针压缩指向问题部分
            // 对于后续的CNAME记录，需要写入完整的域名
            if (current == first_record)
            {
                // 第一个记录，使用指针压缩
                if (offset + 2 > buf_size)
                    return -1;
                buffer[offset++] = 0xC0;
                buffer[offset++] = 0x0C; // 指向偏移量12（问题部分的域名）
            }
            else
            {
                // 后续记录，写入完整域名
                const char *domain_name = current->domain;
                while (*domain_name)
                {
                    char *dot = strchr(domain_name, '.');
                    int len = dot ? (dot - domain_name) : strlen(domain_name);

                    if (offset + len + 1 > buf_size)
                        return -1;
                    buffer[offset++] = len;
                    memcpy(buffer + offset, domain_name, len);
                    offset += len;

                    domain_name = dot ? dot + 1 : domain_name + len;
                }
                buffer[offset++] = 0; // 域名结束
            }

            // 写入TYPE, CLASS, TTL
            if (offset + 10 > buf_size)
                return -1;
            uint16_t type = htons(current->type); // 使用当前记录的实际类型
            uint16_t class = htons(1);
            memcpy(buffer + offset, &type, 2);
            offset += 2;
            memcpy(buffer + offset, &class, 2);
            offset += 2;

            time_t current_time = time(NULL);
            uint32_t ttl = (current->expire_time > current_time) ? (current->expire_time - current_time) : 0;
            uint32_t ttl_net = htonl(ttl);
            memcpy(buffer + offset, &ttl_net, 4);
            offset += 4;

            // 写入RDLENGTH和RDATA
            if (current->type == RR_A)
            {
                if (offset + 6 > buf_size)
                    return -1;
                uint16_t rdlength = htons(4);
                memcpy(buffer + offset, &rdlength, 2);
                offset += 2;
                memcpy(buffer + offset, &current->data.ip_addr.addr.ipv4, 4);
                offset += 4;

                // 打印IP地址调试信息
                uint32_t ip = current->data.ip_addr.addr.ipv4;
                printf("  Added A record: %u.%u.%u.%u (TTL: %u)\n", (ip >> 24) & 0xFF, (ip >> 16) & 0xFF, (ip >> 8) & 0xFF, ip & 0xFF, ttl);
            }
            else if (current->type == RR_AAAA)
            {
                if (offset + 18 > buf_size)
                    return -1;
                uint16_t rdlength = htons(16);
                memcpy(buffer + offset, &rdlength, 2);
                offset += 2;
                memcpy(buffer + offset, current->data.ip_addr.addr.ipv6, 16);
                offset += 16;
                printf("  Added AAAA record (TTL: %u)\n", ttl);
            }
            else if (current->type == RR_CNAME)
            {
                // CNAME记录处理
                const char *cname = current->data.cname;
                int name_wire_len = 0;
                const char *temp_name = cname;

                // 计算编码后的域名长度
                while (*temp_name)
                {
                    char *dot = strchr(temp_name, '.');
                    int label_len = dot ? (dot - temp_name) : strlen(temp_name);
                    name_wire_len += label_len + 1;
                    temp_name = dot ? dot + 1 : temp_name + label_len;
                }
                name_wire_len += 1;

                if (offset + 2 + name_wire_len > buf_size)
                    return -1;
                uint16_t rdlength = htons(name_wire_len);
                memcpy(buffer + offset, &rdlength, 2);
                offset += 2;

                // 写入CNAME域名
                const char *name_ptr = cname;
                while (*name_ptr)
                {
                    char *dot = strchr(name_ptr, '.');
                    int len = dot ? (dot - name_ptr) : strlen(name_ptr);

                    buffer[offset++] = len;
                    memcpy(buffer + offset, name_ptr, len);
                    offset += len;

                    name_ptr = dot ? dot + 1 : name_ptr + len;
                }
                buffer[offset++] = 0;

                printf("  Added CNAME record: %s -> %s (TTL: %u)\n", query_name, cname, ttl);
            }
        }
        current = current->next;
    }

    printf("Multi-record response built successfully, total length: %d bytes\n", offset);
    return offset;
}

/**
 * 解析CNAME链，返回最终的A/AAAA记录
 * 如果找到A/AAAA记录，返回该记录；如果只找到CNAME记录，返回NULL
 */
DNSRecord *resolve_cname_chain(DNSCache *cache, const char *domain, uint16_t query_type)
{
    char current_domain[DOMAIN_MAX_LEN];
    strncpy(current_domain, domain, DOMAIN_MAX_LEN - 1);
    current_domain[DOMAIN_MAX_LEN - 1] = '\0';

    int cname_depth = 0;
    const int MAX_CNAME_DEPTH = 10; // 防止CNAME循环

    while (cname_depth < MAX_CNAME_DEPTH)
    {
        DNSRecord *record = query_cache(cache, current_domain);
        if (!record)
        {
            return NULL; // 缓存中没有找到
        }

        if (record->type == query_type)
        {
            // 找到目标类型的记录
            return record;
        }
        else if (record->type == RR_CNAME)
        {
            // 继续遍历CNAME链
            printf("Following CNAME: %s -> %s\n", current_domain, record->data.cname);
            strncpy(current_domain, record->data.cname, DOMAIN_MAX_LEN - 1);
            current_domain[DOMAIN_MAX_LEN - 1] = '\0';
            cname_depth++;
        }
        else
        {
            // 找到的记录类型不匹配
            return NULL;
        }
    }

    printf("CNAME chain too deep for %s\n", domain);
    return NULL;
}

/**
 * 收集完整的CNAME链，包括CNAME记录和最终的A/AAAA记录
 * 返回一个链接的记录列表，用于构建包含CNAME的完整响应
 * 注意：这个函数创建临时链表，不会修改缓存中的记录
 */
DNSRecord *collect_cname_chain_with_records(DNSCache *cache, const char *domain, uint16_t query_type)
{
    char current_domain[DOMAIN_MAX_LEN];
    strncpy(current_domain, domain, DOMAIN_MAX_LEN - 1);
    current_domain[DOMAIN_MAX_LEN - 1] = '\0';

    int cname_depth = 0;
    const int MAX_CNAME_DEPTH = 10; // 防止CNAME循环

    DNSRecord *first_record = NULL;
    DNSRecord *last_record = NULL;
    bool final_record_found = false; // 新增标志位，追踪是否找到最终记录

    while (cname_depth < MAX_CNAME_DEPTH)
    {
        DNSRecord *record = query_cache(cache, current_domain);
        if (!record)
        {
            break; // 缓存中没有找到，退出循环
        }

        if (record->type == RR_CNAME)
        {
            // 创建CNAME记录的副本并添加到临时链表
            DNSRecord *cname_copy = (DNSRecord *)malloc(sizeof(DNSRecord));
            memcpy(cname_copy, record, sizeof(DNSRecord));
            cname_copy->next = NULL; // 清除原有的next指针

            if (!first_record)
            {
                first_record = cname_copy;
                last_record = cname_copy;
            }
            else
            {
                last_record->next = cname_copy;
                last_record = cname_copy;
            }

            printf("Added CNAME to chain: %s -> %s\n", current_domain, record->data.cname);
            strncpy(current_domain, record->data.cname, DOMAIN_MAX_LEN - 1);
            current_domain[DOMAIN_MAX_LEN - 1] = '\0';
            cname_depth++;
        }
        else if (record->type == query_type)
        {
            // 找到目标类型的记录，收集同一域名的所有该类型记录
            DNSRecord *current_record = record;
            while (current_record)
            {
                if (current_record->type == query_type)
                {
                    // 创建记录的副本并添加到链表末尾
                    DNSRecord *final_copy = (DNSRecord *)malloc(sizeof(DNSRecord));
                    memcpy(final_copy, current_record, sizeof(DNSRecord));
                    final_copy->next = NULL; // 清除原有的next指针

                    if (!first_record)
                    {
                        first_record = final_copy;
                        last_record = final_copy;
                    }
                    else
                    {
                        last_record->next = final_copy;
                        last_record = final_copy;
                    }
                    printf("Added final %s record to chain: %s\n", (query_type == RR_A) ? "A" : "AAAA", current_domain);
                }
                current_record = current_record->next;
            }
            final_record_found = true; // 成功找到最终记录
            break;                     // 找到后退出主循环
        }
        else
        {
            // 找到的记录类型不匹配
            printf("Final domain %s has no %s record in cache\n", current_domain, (query_type == RR_A) ? "A" : "AAAA");
            break;
        }
    }

    if (cname_depth >= MAX_CNAME_DEPTH)
    {
        printf("CNAME chain too deep for %s\n", domain);
    }

    // 只有在完整解析并找到最终记录时才返回结果，否则返回NULL
    if (final_record_found)
    {
        return first_record;
    }
    else
    {
        if (first_record)
        {
            // 如果创建了部分链但未成功，释放内存
            free_temp_record_chain(first_record);
        }
        return NULL;
    }
}

/**
 * 释放由collect_cname_chain_with_records创建的临时链表
 */
void free_temp_record_chain(DNSRecord *first_record)
{
    DNSRecord *current = first_record;
    while (current)
    {
        DNSRecord *next = current->next;
        free(current);
        current = next;
    }
}

void receiveClient(void)
{
    DNS_message msg;
    int recvLen = recvfrom(client_sock, buffer, BUF_SIZE, 0, (struct sockaddr *)&clientAddr, &addr_len);

    printf("Received DNS packet from %s:%d, length = %d bytes\n", inet_ntoa(clientAddr.sin_addr), ntohs(clientAddr.sin_port), recvLen);

    if (recvLen < 0)
    {
        perror("recvfrom failed");
        return;
    }

    // 解析DNS报文
    parse_dns_packet(&msg, buffer, recvLen);

    // 检查缓存（这里假设只检查第一个问题）
    char *query_name = NULL;
    uint16_t query_type = 0;
    if (msg.header->ques_num > 0 && msg.question != NULL)
    {
        query_name = msg.question[0].qname;
        query_type = msg.question[0].qtype;
    }
    if (query_name != NULL)
    {
        printf("query_name : %s  %d \n", query_name, query_type);
    }

    uint16_t client_txid = msg.header->transactionID;
    // 保存客户端地址以便后续回复
    struct sockaddr_in original_client = clientAddr;

    /*
    当接收到从客户端来的DNS查询时，首先检查域名是否在黑名单中。
    如果在黑名单中，则构建一个NXDOMAIN响应并发送回客户端。
    */
    if (query_name != NULL && domain_blacklist != NULL)
    {
        if (is_domain_blocked(domain_blacklist, query_name))
        {
            printf("Domain %s is BLOCKED, returning NXDOMAIN response\n", query_name);

            // 构建NXDOMAIN响应
            int response_len = build_nxdomain_response((unsigned char *)buffer, BUF_SIZE, client_txid, query_name, query_type);
            if (response_len > 0)
            {
                // 发送NXDOMAIN响应给客户端
                sendto(client_sock, buffer, response_len, 0, (struct sockaddr *)&original_client, addr_len);
                printf("Sent NXDOMAIN response for blocked domain: %s\n", query_name);
            }
            else
            {
                printf("Failed to build NXDOMAIN response for: %s\n", query_name);
            }
            return;
        }
    }

    print_cache_status(dns_cache);

    // 1. 先查询缓存，支持CNAME链解析
    DNSRecord *cached_record = NULL;
    int is_temp_chain = 0; // 标记是否为临时链表

    if (query_name != NULL && (msg.question->qtype == RR_A || msg.question->qtype == RR_AAAA || msg.question->qtype == RR_CNAME))
    {
        if (msg.question->qtype == RR_CNAME)
        {
            // 直接查询CNAME记录
            cached_record = query_cache_all_records(dns_cache, query_name, RR_CNAME);
        }
        else
        {
            // A/AAAA记录使用新的多记录查询
            cached_record = query_cache_all_records(dns_cache, query_name, msg.question->qtype);
            if (!cached_record)
            {
                // 如果没有直接的A/AAAA记录，尝试收集完整的CNAME链
                cached_record = collect_cname_chain_with_records(dns_cache, query_name, msg.question->qtype);
                is_temp_chain = 1; // 标记为临时链表
            }
        }
    }

    // 2. 如果缓存命中
    if (cached_record != NULL)
    {
        printf("Cache hit for: %s\n", query_name);

        // 使用多记录响应构建函数
        int response_len = build_multi_record_response((unsigned char *)buffer, BUF_SIZE, client_txid, query_name, query_type, cached_record);

        // 如果是CNAME或者RR_A查询，打印要发送的字节数据
        if ((query_type == RR_CNAME || query_type == RR_A) && response_len > 0)
        {
            LOG_BYTE("=== CNAME Response Bytes Debug (Length: %d) ===\n", response_len);
            for (int i = 0; i < response_len; i++)
            {
                LOG_BYTE("%02X ", (unsigned char)buffer[i]);
                if ((i + 1) % 16 == 0)
                    LOG_BYTE("\n"); // 每16字节换行
            }
            if (response_len % 16 != 0)
                LOG_BYTE("\n"); // 最后一行换行

            // 也打印ASCII可读部分
            LOG_BYTE("ASCII representation:\n");
            for (int i = 0; i < response_len; i++)
            {
                char c = buffer[i];
                if (c >= 32 && c <= 126)
                {
                    LOG_BYTE("%c", c);
                }
                else
                {
                    LOG_BYTE(".");
                }
                if ((i + 1) % 64 == 0)
                    LOG_BYTE("\n"); // 每64字符换行
            }
            if (response_len % 64 != 0)
                LOG_BYTE("\n");
            LOG_BYTE("===============================================\n");
        }

        // 发送缓存响应给客户端
        sendto(client_sock, buffer, response_len, 0, (struct sockaddr *)&original_client, addr_len);

        // 如果使用了临时链表，需要释放
        if (is_temp_chain)
        {
            free_temp_record_chain(cached_record);
        }

        return;
    }
    else
    {
        printf("Cache miss for: %s, forwarding to remote DNS\n", query_name);

        // 查找空闲槽位
        cleanup_timeouts();
        int slot = find_free_slot();
        if (slot == -1)
        {
            printf("No free slots available, dropping request\n");
            return;
        }

        // 保存事务ID和客户端信息
        ID_list[slot].orig_id = client_txid;
        ID_list[slot].cli = original_client;
        ID_list[slot].timestamp = time(NULL);
        ID_used[slot] = true;

        // 修改事务ID为槽位索引+1，避免冲突
        uint16_t new_txid = slot + 1;
        buffer[0] = (new_txid >> 8) & 0xFF;
        buffer[1] = new_txid & 0xFF;

        // 转发请求到远程DNS服务器
        sendto(server_sock, buffer, recvLen, 0, (struct sockaddr *)&server_addr, sizeof(server_addr));
    }
}

void receiveServer(void)
{
    int remote_addr_len = sizeof(server_addr);
    int remote_recvLen = recvfrom(server_sock, buffer, BUF_SIZE, 0, (struct sockaddr *)&server_addr, &remote_addr_len);

    if (remote_recvLen > 0)
    {
        printf("Received response from remote DNS, length = %d bytes\n", remote_recvLen);

        // 获取服务器响应中的事务ID
        uint16_t server_txid = (buffer[0] << 8) | buffer[1];

        // 检查事务ID是否有效
        if (server_txid == 0 || server_txid > MAX_INFLIGHT)
        {
            printf("Invalid transaction ID in response: %d\n", server_txid);
            return;
        }

        int slot = server_txid - 1; // 转换为槽位索引，由于事务ID是通过数组的槽位加一产生，所以这里需要减一

        // 检查事务ID是否在使用中
        if (!ID_used[slot])
        {
            printf("No matching request found for transaction ID %d\n", server_txid);
            return;
        }

        // 恢复原始事务ID，将从服务器返回的ID映射到原始客户端的事务ID，并发送到服务器
        uint16_t orig_txid = ID_list[slot].orig_id;
        buffer[0] = (orig_txid >> 8) & 0xFF;
        buffer[1] = orig_txid & 0xFF;

        // 获取原始客户端地址
        struct sockaddr_in original_client = ID_list[slot].cli;

        // 解析DNS报文以获取查询名和响应记录
        DNS_message response_msg;
        parse_dns_packet(&response_msg, buffer, remote_recvLen);

        char *query_name = NULL;
        if (response_msg.header->ques_num > 0 && response_msg.question != NULL)
        {
            query_name = response_msg.question[0].qname;
        }

        // 缓存远程服务器的响应（Answer Section）
        if (query_name != NULL && response_msg.header->ans_num > 0 && response_msg.answer != NULL)
        {
            for (int i = 0; i < response_msg.header->ans_num; i++)
            {
                DNS_resource_record *rr = &response_msg.answer[i];

                if (rr->type == RR_A)
                {
                    uint32_t ipv4_addr;
                    memcpy(&ipv4_addr, rr->data.a_record.IP_addr, 4);
                    // 使用资源记录的实际名称作为缓存键，而不是查询名称
                    add_to_cache(dns_cache, rr->name, RR_A, &ipv4_addr, rr->ttl);
                    printf("Cached A record: %s -> %d.%d.%d.%d, TTL: %u\n", rr->name, rr->data.a_record.IP_addr[0], rr->data.a_record.IP_addr[1],
                           rr->data.a_record.IP_addr[2], rr->data.a_record.IP_addr[3], rr->ttl);
                }
                else if (rr->type == RR_AAAA)
                {
                    uint8_t ipv6_addr[16];
                    memcpy(&ipv6_addr, rr->data.aaaa_record.IP_addr, 16);
                    add_to_cache(dns_cache, rr->name, RR_AAAA, &ipv6_addr, rr->ttl);
                    printf("Cached AAAA record: %s -> [IPv6], TTL: %u\n", rr->name, rr->ttl);
                }
                else if (rr->type == RR_CNAME)
                {
                    // 缓存CNAME记录
                    add_to_cache(dns_cache, rr->name, RR_CNAME, rr->data.cname_record.name, rr->ttl);
                    printf("Cached Additional CNAME record: %s -> %s, TTL: %u\n", rr->name, rr->data.cname_record.name, rr->ttl);
                }
            }
        }

        // 缓存远程服务器的响应（Additional Section）
        if (query_name != NULL && response_msg.header->add_num > 0 && response_msg.additional != NULL)
        {
            printf("Processing %d additional records\n", response_msg.header->add_num);
            for (int i = 0; i < response_msg.header->add_num; i++)
            {
                DNS_resource_record *rr = &response_msg.additional[i];

                if (rr->type == RR_A)
                {
                    uint32_t ipv4_addr;
                    memcpy(&ipv4_addr, rr->data.a_record.IP_addr, 4);
                    add_to_cache(dns_cache, rr->name, RR_A, &ipv4_addr, rr->ttl);
                    printf("Cached Additional A record: %s -> %d.%d.%d.%d, TTL: %u\n", rr->name, rr->data.a_record.IP_addr[0],
                           rr->data.a_record.IP_addr[1], rr->data.a_record.IP_addr[2], rr->data.a_record.IP_addr[3], rr->ttl);
                }
                else if (rr->type == RR_AAAA)
                {
                    uint8_t ipv6_addr[16];
                    memcpy(&ipv6_addr, rr->data.aaaa_record.IP_addr, 16);
                    add_to_cache(dns_cache, rr->name, RR_AAAA, &ipv6_addr, rr->ttl);
                    printf("Cached Additional AAAA record: %s -> [IPv6], TTL: %u\n", rr->name, rr->ttl);
                }
                else if (rr->type == RR_CNAME)
                {
                    add_to_cache(dns_cache, rr->name, RR_CNAME, rr->data.cname_record.name, rr->ttl);
                    printf("Cached Additional CNAME record: %s -> %s, TTL: %u\n", rr->name, rr->data.cname_record.name, rr->ttl);
                }
            }
        }

        // 将响应返回给原始客户端
        sendto(client_sock, buffer, remote_recvLen, 0, (struct sockaddr *)&original_client, addr_len);

        // 释放槽位
        ID_used[slot] = false;
    }
}

/*
 * 构建DNS NXDOMAIN响应（域名不存在错误）
 * 用于拦截域名时返回给客户端
 */
int build_nxdomain_response(unsigned char *buffer, int buf_size, uint16_t transactionID, const char *query_name, uint16_t query_type)
{
    if (!buffer || !query_name || buf_size < 12)
    {
        return -1;
    }

    int offset = 0;

    // DNS Header
    buffer[0] = (transactionID >> 8) & 0xFF; // Transaction ID 高字节
    buffer[1] = transactionID & 0xFF;        // Transaction ID 低字节
    offset += 2;

    // Flags: QR=1(response), OPCODE=0(query), AA=0, TC=0, RD=1, RA=1, Z=000, RCODE=3(NXDOMAIN)
    uint16_t flags = 0x8183; // 1000 0001 1000 0011
    buffer[2] = (flags >> 8) & 0xFF;
    buffer[3] = flags & 0xFF;
    offset += 2;

    // Questions: 1
    buffer[4] = 0x00;
    buffer[5] = 0x01;
    offset += 2;

    // Answer RRs: 0
    buffer[6] = 0x00;
    buffer[7] = 0x00;
    offset += 2;

    // Authority RRs: 0
    buffer[8] = 0x00;
    buffer[9] = 0x00;
    offset += 2;

    // Additional RRs: 0
    buffer[10] = 0x00;
    buffer[11] = 0x00;
    offset += 2;

    // Question section
    // Encode domain name
    const char *p = query_name;
    while (*p)
    {
        size_t len = 0;
        const char *dot = strchr(p, '.');
        if (dot)
        {
            len = dot - p;
        }
        else
        {
            len = strlen(p);
        }

        if (offset + len + 1 > buf_size)
        {
            return -1;
        }

        buffer[offset++] = (char)len;
        memcpy(buffer + offset, p, len);
        offset += len;

        if (dot)
        {
            p = dot + 1;
        }
        else
        {
            p += len;
        }
    }
    buffer[offset++] = '\0'; // Null terminator

    // QTYPE
    if (offset + 2 > buf_size)
    {
        return -1;
    }
    buffer[offset] = (query_type >> 8) & 0xFF;
    buffer[offset + 1] = query_type & 0xFF;
    offset += 2;

    // QCLASS (IN = 1)
    if (offset + 2 > buf_size)
    {
        return -1;
    }
    buffer[offset] = 0x00;
    buffer[offset + 1] = 0x01;
    offset += 2;

    return offset;
}