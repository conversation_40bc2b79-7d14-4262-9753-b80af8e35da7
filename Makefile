# Makefile

# 编译器和选项
CC      := gcc
CFLAGS  := -Wall -O2 -Iinc -fcommon
LDFLAGS := -lws2_32

# 源码和目标
SRCDIR  := src
BUILDDIR:= build
SOURCES := $(wildcard $(SRCDIR)/*.c)
OBJECTS := $(patsubst $(SRCDIR)/%.c,$(BUILDDIR)/%.o,$(SOURCES))

# 最终可执行文件
TARGET  := dnsserver.exe
TEST_TARGET := test_host.exe

.PHONY: all clean test

all: $(TARGET)

# 链接生成可执行文件
$(TARGET): $(OBJECTS)
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)

# 编译每个 .c 为 .o
# 并确保 build 目录存在
$(BUILDDIR)/%.o: $(SRCDIR)/%.c | $(BUILDDIR)
	$(CC) $(CFLAGS) -c $< -o $@

# 创建 build 目录
$(BUILDDIR):
	mkdir -p $(BUILDDIR)

# 编译测试程序
test: $(TEST_TARGET)

$(TEST_TARGET): test_host.c $(filter-out $(BUILDDIR)/main.o,$(OBJECTS))
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)

# 清理中间文件和可执行文件
clean:
	rm -rf $(BUILDDIR) $(TARGET) $(TEST_TARGET)
