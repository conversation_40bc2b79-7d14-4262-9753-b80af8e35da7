#include "../inc/LRU.h"
#include "../inc/blacklist.h"
#include "../inc/header.h"
#include "../inc/host.h"
#include "../inc/log.h"

// 创建新的Trie节点
TrieNode* create_trie_node() {
    TrieNode* node = (TrieNode*)malloc(sizeof(TrieNode));
    memset(node->children, 0, sizeof(node->children));
    node->record = NULL;
    node->is_end = 0;
    return node;
}

// 初始化DNS缓存
DNSCache* init_dns_cache(int capacity) {
    DNSCache* cache = (DNSCache*)malloc(sizeof(DNSCache));
    cache->root = create_trie_node();
    cache->lru_head = cache->lru_tail = NULL;
    cache->capacity = capacity;
    cache->size = 0;
    return cache;
}

// 将域名转换成小写并验证
void normalize_domain(char* domain) {
    for (int i = 0; domain[i]; i++) {
        if (domain[i] >= 'A' && domain[i] <= 'Z') {
            domain[i] += 'a' - 'A';
        } else if (domain[i] != '.' && domain[i] != '-' && !(domain[i] >= 'a' && domain[i] <= 'z') && !(domain[i] >= '0') && !(domain[i] <= '9')) {
            domain[i] = '\0';  // 非法字符截断
            break;
        }
    }
}

// 向Trie树当中添加记录
void trie_insert(DNSCache* cache, DNSRecord* record) {
    char domain[DOMAIN_MAX_LEN];
    strcpy(domain, record->domain);
    normalize_domain(domain);
    TrieNode* curr = cache->root;
    for (int i = strlen(domain) - 1; i >= 0; i--) {
        char c = domain[i];
        if (c == '.') {
            continue;
        }
        int index = c - 'a';
        if (c >= 'a' && c <= 'z') {
            index = c - 'a';
        } else if (c >= '0' && c <= '9') {
            index = 26 + c - '0';
        } else {
            return;  // 非法字符
        }
        // if (index < 0 || index >= 26)
        //     continue;
        if (!curr->children[index]) {
            curr->children[index] = create_trie_node();
        }
        curr = curr->children[index];
    }
    curr->is_end = 1;
    curr->record = record;
}

// 从Trie树中查找记录
DNSRecord* trie_search(DNSCache* cache, const char* domain) {
    char normalized[DOMAIN_MAX_LEN];
    strcpy(normalized, domain);
    normalize_domain(normalized);
    // printf("normalized = %s\n", normalized);

    TrieNode* curr = cache->root;
    for (int i = strlen(normalized) - 1; i >= 0; i--) {
        char c = normalized[i];
        // printf("c = %c\n", c);  // 调试用
        if (c == '.')
            continue;
        int index = -1;
        if (c >= 'a' && c <= 'z') {
            index = c - 'a';
        } else if (c >= '0' && c <= '9') {
            index = 26 + c - '0';
        } else {
            return NULL;  // 非法字符
        }
        if (!curr->children[index]) {
            return NULL;
        }

        curr = curr->children[index];
    }

    return (curr && curr->is_end) ? curr->record : NULL;
}

// 更新LRU链表中的记录位置
void lru_update(DNSCache* cache, DNSRecord* record) {
    if (record == cache->lru_head)
        return;

    // 从原位置移除
    if (record->lru_prev)
        record->lru_prev->lru_next = record->lru_next;
    if (record->lru_next)
        record->lru_next->lru_prev = record->lru_prev;

    // 如果是尾节点且不是唯一节点
    if (record == cache->lru_tail && record->lru_prev) {
        cache->lru_tail = record->lru_prev;
    }

    // 插入到头部
    record->lru_next = cache->lru_head;
    record->lru_prev = NULL;

    if (cache->lru_head) {
        cache->lru_head->lru_prev = record;
    }
    cache->lru_head = record;

    // 如果之前缓存为空
    if (!cache->lru_tail) {
        cache->lru_tail = record;
    }

    record->last_used = time(NULL);
}

// 淘汰最久未使用的记录
void lru_evict(DNSCache* cache) {
    if (!cache->lru_tail || cache->size < cache->capacity)
        return;

    DNSRecord* to_evict = cache->lru_tail;

    // 从Trie树中删除
    char normalized[DOMAIN_MAX_LEN];
    strcpy(normalized, to_evict->domain);
    normalize_domain(normalized);

    TrieNode* curr = cache->root;
    for (int i = strlen(normalized) - 1; i >= 0; i--) {
        char c = normalized[i];
        if (c == '.')
            continue;

        int index = c - 'a';
        if (index < 0 || index >= 26)
            break;

        if (!curr->children[index])
            break;
        curr = curr->children[index];
    }

    if (curr && curr->is_end) {
        curr->is_end = 0;
        curr->record = NULL;
    }

    // 从LRU链表中移除
    if (to_evict->lru_prev) {
        to_evict->lru_prev->lru_next = NULL;
    }
    cache->lru_tail = to_evict->lru_prev;

    if (to_evict == cache->lru_head) {
        cache->lru_head = NULL;
    }

    free(to_evict);
    cache->size--;
}

// 添加记录到缓存
void add_to_cache(DNSCache* cache, const char* domain, int addr_type, void* addr, time_t ttl) {
    // 先检查是否已存在同类型的记录
    DNSRecord* existing = trie_search(cache, domain);
    if (existing) {
        // 检查链表中是否已经有相同类型和数据的记录
        DNSRecord* current = existing;
        while (current) {
            if (current->type == addr_type) {
                bool duplicate = false;
                if (addr_type == RR_A) {
                    duplicate = (current->data.ip_addr.addr.ipv4 == *(uint32_t*)addr);
                } else if (addr_type == RR_AAAA) {
                    duplicate = (memcmp(current->data.ip_addr.addr.ipv6, addr, 16) == 0);
                } else if (addr_type == RR_CNAME) {
                    duplicate = (strcmp(current->data.cname, (char*)addr) == 0);
                }

                if (duplicate) {
                    // 更新过期时间并移动到LRU头部
                    current->expire_time = time(NULL) + ttl;
                    lru_update(cache, current);
                    return;
                }
            }
            current = current->next;
        }

        // 没有找到重复记录，在链表末尾添加新记录
        DNSRecord* new_record = (DNSRecord*)malloc(sizeof(DNSRecord));
        strncpy(new_record->domain, domain, DOMAIN_MAX_LEN - 1);
        new_record->domain[DOMAIN_MAX_LEN - 1] = '\0';
        new_record->type = addr_type;

        if (addr_type == RR_A) {
            new_record->data.ip_addr.addr.ipv4 = *(uint32_t*)addr;
            printf("Added additional A record: %s -> %u.%u.%u.%u\n", domain, (*(uint32_t*)addr >> 24) & 0xFF, (*(uint32_t*)addr >> 16) & 0xFF,
                   (*(uint32_t*)addr >> 8) & 0xFF, *(uint32_t*)addr & 0xFF);
        } else if (addr_type == RR_AAAA) {
            memcpy(new_record->data.ip_addr.addr.ipv6, addr, 16);
            printf("Added additional AAAA record for %s\n", domain);
        } else if (addr_type == RR_CNAME) {
            strncpy(new_record->data.cname, (char*)addr, DOMAIN_MAX_LEN - 1);
            new_record->data.cname[DOMAIN_MAX_LEN - 1] = '\0';
            printf("Added additional CNAME record: %s -> %s\n", domain, (char*)addr);
        }

        new_record->expire_time = time(NULL) + ttl;
        new_record->last_used = time(NULL);
        new_record->next = NULL;
        new_record->lru_prev = new_record->lru_next = NULL;

        // 添加到链表末尾
        current = existing;
        while (current->next) {
            current = current->next;
        }
        current->next = new_record;

        // 添加到LRU链表
        lru_update(cache, new_record);
        cache->size++;
        return;
    }

    // 如果缓存已满，淘汰最久未使用的记录
    if (cache->size >= cache->capacity) {
        lru_evict(cache);
    }

    // 创建新记录
    DNSRecord* record = (DNSRecord*)malloc(sizeof(DNSRecord));
    strncpy(record->domain, domain, DOMAIN_MAX_LEN - 1);
    record->domain[DOMAIN_MAX_LEN - 1] = '\0';
    record->type = addr_type;

    if (addr_type == RR_A) {
        record->data.ip_addr.addr.ipv4 = *(uint32_t*)addr;
        // printf("new ip %d\n", record->data.ip_addr.addr.ipv4);
    } else if (addr_type == RR_AAAA) {
        memcpy(record->data.ip_addr.addr.ipv6, addr, 16);
        // printf("new ipv6 %02x:%02x:%02x:%02x:%02x:%02x:%02x:%02x\n", record->data.ip_addr.addr.ipv6[0], record->data.ip_addr.addr.ipv6[1],
        //        record->data.ip_addr.addr.ipv6[2], record->data.ip_addr.addr.ipv6[3], record->data.ip_addr.addr.ipv6[4],
        //        record->data.ip_addr.addr.ipv6[5], record->data.ip_addr.addr.ipv6[6], record->data.ip_addr.addr.ipv6[7]);
        LOG_DEBUG("new ipv6 %02x:%02x:%02x:%02x:%02x:%02x:%02x:%02x\n", record->data.ip_addr.addr.ipv6[0], record->data.ip_addr.addr.ipv6[1],
                  record->data.ip_addr.addr.ipv6[2], record->data.ip_addr.addr.ipv6[3], record->data.ip_addr.addr.ipv6[4],
                  record->data.ip_addr.addr.ipv6[5], record->data.ip_addr.addr.ipv6[6], record->data.ip_addr.addr.ipv6[7]);
    } else if (addr_type == RR_CNAME) {
        strncpy(record->data.cname, (char*)addr, DOMAIN_MAX_LEN - 1);
        record->data.cname[DOMAIN_MAX_LEN - 1] = '\0';
        // printf("new cname %s -> %s\n", domain, record->data.cname);
    }

    record->expire_time = time(NULL) + ttl;
    record->last_used = time(NULL);
    record->next = NULL;
    record->lru_prev = record->lru_next = NULL;

    // 添加到Trie树
    trie_insert(cache, record);

    // 添加到LRU链表头部
    lru_update(cache, record);

    cache->size++;
}

// 从缓存查询记录
DNSRecord* query_cache(DNSCache* cache, const char* domain) {
    DNSRecord* record = trie_search(cache, domain);
    if (!record)
        return NULL;

    // 检查是否过期
    // if (time(NULL) > record->expire_time)
    // {
    //     // 从缓存中移除过期记录
    //     lru_evict(cache);
    //     return NULL;
    // }

    // 更新LRU
    lru_update(cache, record);

    return record;
}

// 查询指定域名和类型的所有记录
DNSRecord* query_cache_all_records(DNSCache* cache, const char* domain, uint16_t type) {
    DNSRecord* first_record = trie_search(cache, domain);
    if (!first_record) {
        return NULL;
    }

    // TTL过期检查
    time_t current_time = time(NULL);
    if (first_record->expire_time < current_time) {
        // 记录已过期，理想情况下应该在这里触发删除操作
        // 为简单起见，直接返回NULL，视为缓存未命中
        return NULL;
    }

    DNSRecord* head = NULL;
    DNSRecord* tail = NULL;

    // 遍历链表，找到第一个匹配类型的记录
    DNSRecord* current = first_record;
    while (current) {
        if (current->type == type) {
            // 更新LRU（只更新第一个匹配的记录）
            lru_update(cache, current);
            return current;
        }
        current = current->next;
    }

    return NULL;
}

// 打印缓存状态(调试用)
void print_cache_status(DNSCache* cache) {
    printf("Cache status: %d/%d\n", cache->size, cache->capacity);
    printf("================================= LRU order =================================\n");

    DNSRecord* curr = cache->lru_head;
    while (curr) {
        printf("| domain: %-40s type: %2d              -> |\n", curr->domain, curr->type);
        curr = curr->lru_next;
    }
    printf("=================================== NULL ====================================\n");
}

int ip_str_to_bytes_sscanf(const char* ip_str, uint8_t* bytes) {
    if (!ip_str || !bytes)
        return -1;

    unsigned int b0, b1, b2, b3;
    if (sscanf(ip_str, "%u.%u.%u.%u", &b0, &b1, &b2, &b3) != 4) {
        return -1;
    }

    // 检查每个字节是否在0-255范围内
    if (b0 > 255 || b1 > 255 || b2 > 255 || b3 > 255) {
        return -1;
    }

    bytes[0] = (uint8_t)b0;
    bytes[1] = (uint8_t)b1;
    bytes[2] = (uint8_t)b2;
    bytes[3] = (uint8_t)b3;

    return 0;
}

void init_DNS(void) {
    dns_cache = init_dns_cache(500);  // 初始化DNS缓存，容量为500

    // 初始化域名拦截表
    domain_blacklist = init_blacklist();
    if (domain_blacklist == NULL) {
        // printf("Failed to initialize domain blacklist\n");
        LOG_INFO("Failed to initialize domain blacklist\n");
    } else {
        // printf("Domain blacklist initialized successfully\n");
        LOG_INFO("Domain blacklist initialized successfully\n");
        print_blacklist(domain_blacklist);
    }

    // 从 hosts 文件初始化 DNS 缓存和拦截表
    init_hosts_to_dns_cache();

    // 先手动添加一个默认的DNS记录（作为备用）
    uint32_t ipv4;
    inet_pton(AF_INET, "***********", &ipv4);
    ip_str_to_bytes_sscanf("***********", (uint8_t*)&ipv4);
    // printf("ipv4 %u\n", ipv4);
    add_to_cache(dns_cache, "example.com", RR_A, &ipv4, 3600);

    uint8_t ipv6[16];
    // 将文本形式的 IP 地址转换为二进制形式的函数
    inet_pton(AF_INET6, "2001:db8:85a3:0000:0000:8a2e:370:7334", ipv6);
    add_to_cache(dns_cache, "ipv.example.com", RR_AAAA, ipv6, 7200);
}