#pragma once

#include "../inc/default.h"
#include "../inc/header.h"

#define MAX_INFLIGHT 1024   // 最大并发未完成转发请求数
#define QUERY_TIMEOUT_SEC 5 // 超时未得到上游响应

/**
 * 在途查询结构体
 * 为了做到客户端ID->我们转发时用的新ID->上游响应回来后再把新ID改回原ID，
 *  需要保存每条在途查询的：
 * orig_id: 客户端最初发来的DNSHeader里的id字段（16 位）
 * cli: 客户端的sockaddr_in结构（IP+端口）
 * timestamp: 记录“我把它发给上游”的时间戳，以便若>QUERY_TIMEOUT_SEC秒后
 *  上游没有回应，就把这个槽位释放
 */
typedef struct
{
    uint16_t orig_id;       // 客户端原始事务ID
    struct sockaddr_in cli; // 原客户端地址（IP+端口）
    time_t timestamp;       // 记录转发时间，用于超时清理
} IDEntry;

// 存储第i个在途查询得信息
extern IDEntry ID_list[MAX_INFLIGHT];
// 为true表示槽位已用，为false表示空闲可复用
extern bool ID_used[MAX_INFLIGHT];

// 寻找空闲槽，有则返回索引，无则返回-1
int find_free_slot(void);

// 清理超时槽
void cleanup_timeouts(void);
