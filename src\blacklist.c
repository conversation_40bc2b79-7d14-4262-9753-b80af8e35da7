#include "../inc/blacklist.h"
#include "../inc/log.h"

DomainBlacklist* domain_blacklist;

/*
 * 初始化域名拦截表
 * 返回新创建的拦截表指针，失败返回NULL
 */
DomainBlacklist* init_blacklist() {
    DomainBlacklist* blacklist = malloc(sizeof(DomainBlacklist));
    if (!blacklist) {
        printf("Failed to allocate memory for blacklist\n");
        LOG_INFO("Failed to allocate memory for blacklist\n");
        return NULL;
    }

    blacklist->count = 0;
    memset(blacklist->entries, 0, sizeof(blacklist->entries));

    // 添加一些默认的拦截域名示例
    add_to_blacklist(blacklist, "ad.doubleclick.net");
    add_to_blacklist(blacklist, "googleads.g.doubleclick.net");
    add_to_blacklist(blacklist, "facebook.com");
    add_to_blacklist(blacklist, "malware.example.com");

    printf("Domain blacklist initialized with %d entries\n", blacklist->count);
    LOG_INFO("Domain blacklist initialized with %d entries\n", blacklist->count);
    return blacklist;
}

/*
 * 向拦截表添加域名
 * 返回0表示成功，-1表示失败
 */
int add_to_blacklist(DomainBlacklist* blacklist, const char* domain) {
    if (!blacklist || !domain) {
        return -1;
    }

    if (blacklist->count >= MAX_BLACKLIST_SIZE) {
        printf("Blacklist is full, cannot add more domains\n");
        LOG_INFO("Blacklist is full, cannot add more domains\n");
        return -1;
    }

    // 检查域名是否已存在
    if (is_domain_blocked(blacklist, domain)) {
        printf("Domain %s is already in blacklist\n", domain);
        LOG_INFO("Domain %s is already in blacklist\n", domain);
        return 0;  // 已存在，返回成功
    }

    // 添加新域名
    strncpy(blacklist->entries[blacklist->count].domain, domain, DOMAIN_MAX_LEN - 1);
    blacklist->entries[blacklist->count].domain[DOMAIN_MAX_LEN - 1] = '\0';
    blacklist->entries[blacklist->count].added_time = time(NULL);
    blacklist->count++;

    // printf("Added domain to blacklist: %s\n", domain);
    LOG_DEBUG("Added domain to blacklist: %s\n", domain);
    return 0;
}

/*
 * 检查域名是否在拦截表中
 * 返回1表示被拦截，0表示未被拦截
 */
int is_domain_blocked(DomainBlacklist* blacklist, const char* domain) {
    if (!blacklist || !domain) {
        return 0;
    }

    for (int i = 0; i < blacklist->count; i++) {
        if (strcmp(blacklist->entries[i].domain, domain) == 0) {
            return 1;  // 域名在拦截表中
        }
    }

    return 0;  // 域名不在拦截表中
}

/*
 * 从拦截表移除域名
 * 返回0表示成功，-1表示失败（域名不存在）
 */
int remove_from_blacklist(DomainBlacklist* blacklist, const char* domain) {
    if (!blacklist || !domain) {
        return -1;
    }

    for (int i = 0; i < blacklist->count; i++) {
        if (strcmp(blacklist->entries[i].domain, domain) == 0) {
            // 找到域名，移动后续元素前移
            for (int j = i; j < blacklist->count - 1; j++) {
                blacklist->entries[j] = blacklist->entries[j + 1];
            }
            blacklist->count--;
            printf("Removed domain from blacklist: %s\n", domain);
            LOG_INFO("Removed domain from blacklist: %s\n", domain);
            return 0;
        }
    }

    printf("Domain not found in blacklist: %s\n", domain);
    LOG_INFO("Domain not found in blacklist: %s\n", domain);
    return -1;
}

/*
 * 打印拦截表内容
 */
void print_blacklist(DomainBlacklist* blacklist) {
    if (!blacklist) {
        printf("Blacklist is NULL\n");
        LOG_INFO("Blacklist is NULL\n");
        return;
    }

    printf("+----------------------------------[ Domain Blacklist ]----------------------------------+\n");
    printf("| Total Entries: %-71d |\n", blacklist->count);
    printf("+----------------------------------------------------------------------------------------+\n");
    LOG_INFO("Domain Blacklist (%d entries)\n", blacklist->count);

    for (int i = 0; i < blacklist->count; i++) {
        char time_str[30];
        strncpy(time_str, ctime(&blacklist->entries[i].added_time), sizeof(time_str));
        time_str[strcspn(time_str, "\n")] = 0;  // 移除 ctime 带来的换行符

        printf("| %-3d. %-47s (Added: %s) |\n", i + 1, blacklist->entries[i].domain, time_str);
        LOG_INFO("| %d. %s (added: %s", i + 1, blacklist->entries[i].domain, time_str);
    }
    printf("+----------------------------------------------------------------------------------------+\n");
}

/*
 * 清理拦截表内存
 */
void cleanup_blacklist(DomainBlacklist* blacklist) {
    if (blacklist) {
        free(blacklist);
        printf("Blacklist cleaned up\n");
        LOG_INFO("Blacklist cleaned up\n");
    }
}